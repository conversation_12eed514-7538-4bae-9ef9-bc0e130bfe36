import { getContract } from 'viem';
import { publicClient } from '../config/wagmi';
import deployment from './deployment.json';

// Import ABIs
import ShareTokenABI from './ShareToken.json';
import PackageManagerABI from './PackageManager.json';
import VestingVaultABI from './VestingVault.json';
import ReferralManagerABI from './ReferralManager.json';
import USDTTokenABI from './BlockCoopTestTether.json';

// Contract instances
export const getShareTokenContract = () => 
  getContract({
    address: deployment.shareToken as `0x${string}`,
    abi: ShareTokenABI.abi,
    publicClient
  });

export const getPackageManagerContract = () => 
  getContract({
    address: deployment.packageManager as `0x${string}`,
    abi: PackageManagerABI.abi,
    publicClient
  });

export const getVestingVaultContract = () => 
  getContract({
    address: deployment.vestingVault as `0x${string}`,
    abi: VestingVaultABI.abi,
    publicClient
  });

export const getReferralManagerContract = () => 
  getContract({
    address: deployment.referralManager as `0x${string}`,
    abi: ReferralManagerABI.abi,
    publicClient
  });

export const getUSDTContract = () => 
  getContract({
    address: deployment.usdtAddress as `0x${string}`,
    abi: USDTTokenABI.abi,
    publicClient
  });