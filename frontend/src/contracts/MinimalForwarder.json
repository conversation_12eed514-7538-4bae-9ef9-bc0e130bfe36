{"_format": "hh-sol-artifact-1", "contractName": "Minimal<PERSON><PERSON><PERSON><PERSON>", "sourceName": "contracts/src/MinimalForwarder.sol", "abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"components": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "gas", "type": "uint256"}, {"internalType": "uint256", "name": "nonce", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "internalType": "struct MinimalForwarder.ForwardRequest", "name": "req", "type": "tuple"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}], "name": "execute", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}, {"internalType": "bytes", "name": "", "type": "bytes"}], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}], "name": "getNonce", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"components": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "gas", "type": "uint256"}, {"internalType": "uint256", "name": "nonce", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "internalType": "struct MinimalForwarder.ForwardRequest", "name": "req", "type": "tuple"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}], "name": "verify", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}