{"_format": "hh-sol-artifact-1", "contractName": "ReferralManager", "sourceName": "contracts/src/ReferralManager.sol", "abi": [{"inputs": [{"internalType": "address", "name": "shareToken_", "type": "address"}, {"internalType": "address", "name": "caller", "type": "address"}, {"internalType": "address", "name": "admin", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "referrer", "type": "address"}, {"indexed": true, "internalType": "address", "name": "buyer", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "ReferralPaid", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "TokensWithdrawn", "type": "event"}, {"inputs": [], "name": "ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "CALLER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "referrer", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "payReferral", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "shareToken", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "withdrawTokens", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}