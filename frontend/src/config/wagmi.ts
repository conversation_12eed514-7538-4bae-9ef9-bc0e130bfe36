import { createAppKit } from '@reown/appkit/react'
import { WagmiAdapter } from '@reown/appkit-adapter-wagmi'
import { mainnet, arbitrum, polygon, base, optimism } from '@reown/appkit/networks'
import { QueryClient } from '@tanstack/react-query'
import { getPublicClient } from '@wagmi/core'

// Define BSC Testnet network
const bscTestnet = {
  id: 97,
  name: 'BSC Testnet',
  nativeCurrency: {
    decimals: 18,
    name: 'BN<PERSON>',
    symbol: 'tBNB',
  },
  rpcUrls: {
    default: {
      http: ['https://bsc-testnet.drpc.org'],
    },
    public: {
      http: ['https://bsc-testnet.drpc.org'],
    },
  },
  blockExplorers: {
    default: {
      name: 'BSCScan Testnet',
      url: 'https://testnet.bscscan.com',
    },
  },
  testnet: true,
}


export const projectId = import.meta.env.VITE_REOWN_PROJECT_ID 

if (!projectId) {
  throw new Error('VITE_REOWN_PROJECT_ID is not set')
}


export const wagmiAdapter = new WagmiAdapter({
  networks: [bscTestnet, mainnet, arbitrum, polygon, base, optimism],
  projectId,
  ssr: false
})


export const queryClient = new QueryClient()


export const appKit = createAppKit({
  adapters: [wagmiAdapter],
  networks: [bscTestnet, mainnet, arbitrum, polygon, base, optimism],
  projectId,
  metadata: {
    name: 'BlockCoop',
    description: 'DeFi-Enabled SACCO Platform',
    url: 'https://blockcoop.com',
    icons: ['https://blockcoop.com/icon.png']
  },
  features: {
    analytics: true,
    email: true,
    socials: ['google', 'x', 'github', 'discord', 'apple'],
    emailShowWallets: true
  },
  themeMode: 'light',
  themeVariables: {
    '--w3m-color-mix': '#4F46E5',
    '--w3m-color-mix-strength': 20,
    '--w3m-accent': '#4F46E5',
    '--w3m-border-radius-master': '8px'
  }
})


export const config = wagmiAdapter.wagmiConfig

// Export public client for use with viem's getContract
export const publicClient = getPublicClient(config)