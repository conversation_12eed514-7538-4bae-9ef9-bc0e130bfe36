import { useAccount, useReadContract, useWriteContract } from 'wagmi';
import { getShareTokenContract } from '../contracts';
import { parseEther, formatEther } from 'viem';

export const useShareToken = () => {
  const { address } = useAccount();
  const { data: writeData, writeContractAsync, isPending, isSuccess, error } = useWriteContract();
  
  // Get balance
  const { data: balance, refetch: refetchBalance } = useReadContract({
    ...getShareTokenContract(),
    functionName: 'balanceOf',
    args: [address],
    enabled: !!address,
  });
  
  // Get total supply
  const { data: totalSupply } = useReadContract({
    ...getShareTokenContract(),
    functionName: 'totalSupply',
  });

  // Format balance for display
  const formattedBalance = balance ? formatEther(balance) : '0';
  
  return {
    balance: formattedBalance,
    totalSupply: totalSupply ? formatEther(totalSupply) : '0',
    refetchBalance,
    isPending,
    isSuccess,
    error
  };
};