import { renderHook, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { usePackageManager } from '../usePackageManager';

// Mock wagmi hooks
vi.mock('wagmi', () => ({
  useAccount: vi.fn(),
  useReadContract: vi.fn(),
  useWriteContract: vi.fn(),
}));

// Mock contracts
vi.mock('../../contracts', () => ({
  getPackageManagerContract: vi.fn(() => ({
    address: '0xPackageManager',
    abi: [],
    read: {
      getPackage: vi.fn(),
    },
  })),
  getUSDTContract: vi.fn(() => ({
    address: '0xUSDT',
    abi: [],
  })),
}));

import { useAccount, useReadContract, useWriteContract } from 'wagmi';
import { getPackageManagerContract, getUSDTContract } from '../../contracts';

const mockUseAccount = useAccount as any;
const mockUseReadContract = useReadContract as any;
const mockUseWriteContract = useWriteContract as any;
const mockGetPackageManagerContract = getPackageManagerContract as any;
const mockGetUSDTContract = getUSDTContract as any;

describe('usePackageManager', () => {
  const mockWriteContractAsync = vi.fn();
  const mockGetPackage = vi.fn();
  const userAddress = '0x1234567890123456789012345678901234567890';

  beforeEach(() => {
    vi.clearAllMocks();

    // Setup default mocks
    mockUseAccount.mockReturnValue({
      address: userAddress,
    });

    mockUseReadContract.mockReturnValue({
      data: 3, // packageCount
    });

    mockUseWriteContract.mockReturnValue({
      writeContractAsync: mockWriteContractAsync,
      isPending: false,
      isSuccess: false,
      error: null,
    });

    mockGetPackageManagerContract.mockReturnValue({
      address: '0xPackageManager',
      abi: [],
      read: {
        getPackage: mockGetPackage,
      },
    });

    mockGetUSDTContract.mockReturnValue({
      address: '0xUSDT',
      abi: [],
    });
  });

  it('should return package count', () => {
    const { result } = renderHook(() => usePackageManager());

    expect(result.current.packageCount).toBe(3);
  });

  it('should get package details', async () => {
    const mockPackageData = {
      entryUSDT: BigInt('**********'),
      priceBps: 1200,
      vestSplitBps: 5000,
      referralRateBps: 500,
      exists: true,
    };

    mockGetPackage.mockResolvedValue(mockPackageData);

    const { result } = renderHook(() => usePackageManager());

    const packageDetails = await result.current.getPackageDetails(0);

    expect(mockGetPackage).toHaveBeenCalledWith([0]);
    expect(packageDetails).toEqual(mockPackageData);
  });

  it('should handle purchase package flow', async () => {
    const mockPackageData = {
      entryUSDT: BigInt('**********'),
      priceBps: 1200,
      vestSplitBps: 5000,
      referralRateBps: 500,
      exists: true,
    };

    mockGetPackage.mockResolvedValue(mockPackageData);
    mockWriteContractAsync.mockResolvedValue({ hash: '0xapproval' });

    const { result } = renderHook(() => usePackageManager());

    await result.current.purchasePackage(0, '0xreferrer');

    // Should call approve first
    expect(mockWriteContractAsync).toHaveBeenNthCalledWith(1, expect.objectContaining({
      functionName: 'approve',
      args: ['0xPackageManager', BigInt('**********')],
    }));

    // Then call purchase
    expect(mockWriteContractAsync).toHaveBeenNthCalledWith(2, expect.objectContaining({
      functionName: 'purchase',
      args: [0, '0xreferrer'],
    }));
  });

  it('should use default referrer address when none provided', async () => {
    const mockPackageData = {
      entryUSDT: BigInt('**********'),
      priceBps: 1200,
      vestSplitBps: 5000,
      referralRateBps: 500,
      exists: true,
    };

    mockGetPackage.mockResolvedValue(mockPackageData);
    mockWriteContractAsync.mockResolvedValue({ hash: '0xhash' });

    const { result } = renderHook(() => usePackageManager());

    await result.current.purchasePackage(0);

    // Should use zero address as default referrer
    expect(mockWriteContractAsync).toHaveBeenNthCalledWith(2, expect.objectContaining({
      functionName: 'purchase',
      args: [0, '******************************************'],
    }));
  });

  it('should throw error when wallet not connected', async () => {
    mockUseAccount.mockReturnValue({
      address: undefined,
    });

    const { result } = renderHook(() => usePackageManager());

    await expect(result.current.purchasePackage(0)).rejects.toThrow('Wallet not connected');
  });

  it('should handle package details fetch error', async () => {
    const error = new Error('Package not found');
    mockGetPackage.mockRejectedValue(error);

    const { result } = renderHook(() => usePackageManager());

    await expect(result.current.getPackageDetails(0)).rejects.toThrow('Package not found');
  });

  it('should handle approval transaction error', async () => {
    const mockPackageData = {
      entryUSDT: BigInt('**********'),
      priceBps: 1200,
      vestSplitBps: 5000,
      referralRateBps: 500,
      exists: true,
    };

    mockGetPackage.mockResolvedValue(mockPackageData);
    mockWriteContractAsync.mockRejectedValueOnce(new Error('Approval failed'));

    const { result } = renderHook(() => usePackageManager());

    await expect(result.current.purchasePackage(0)).rejects.toThrow('Approval failed');
  });

  it('should handle purchase transaction error', async () => {
    const mockPackageData = {
      entryUSDT: BigInt('**********'),
      priceBps: 1200,
      vestSplitBps: 5000,
      referralRateBps: 500,
      exists: true,
    };

    mockGetPackage.mockResolvedValue(mockPackageData);
    mockWriteContractAsync
      .mockResolvedValueOnce({ hash: '0xapproval' }) // Approval succeeds
      .mockRejectedValueOnce(new Error('Purchase failed')); // Purchase fails

    const { result } = renderHook(() => usePackageManager());

    await expect(result.current.purchasePackage(0)).rejects.toThrow('Purchase failed');
  });

  it('should return pending state', () => {
    mockUseWriteContract.mockReturnValue({
      writeContractAsync: mockWriteContractAsync,
      isPending: true,
      isSuccess: false,
      error: null,
    });

    const { result } = renderHook(() => usePackageManager());

    expect(result.current.isPending).toBe(true);
  });

  it('should return success state', () => {
    mockUseWriteContract.mockReturnValue({
      writeContractAsync: mockWriteContractAsync,
      isPending: false,
      isSuccess: true,
      error: null,
    });

    const { result } = renderHook(() => usePackageManager());

    expect(result.current.isSuccess).toBe(true);
  });

  it('should return error state', () => {
    const error = new Error('Transaction failed');
    mockUseWriteContract.mockReturnValue({
      writeContractAsync: mockWriteContractAsync,
      isPending: false,
      isSuccess: false,
      error,
    });

    const { result } = renderHook(() => usePackageManager());

    expect(result.current.error).toBe(error);
  });

  it('should return user address', () => {
    const { result } = renderHook(() => usePackageManager());

    expect(result.current.address).toBe(userAddress);
  });

  it('should handle zero package count', () => {
    mockUseReadContract.mockReturnValue({
      data: undefined,
    });

    const { result } = renderHook(() => usePackageManager());

    expect(result.current.packageCount).toBe(0);
  });
});
