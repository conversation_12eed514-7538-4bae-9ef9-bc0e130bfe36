import { useAccount, useReadContract, useWriteContract } from 'wagmi';
import { getPackageManagerContract, getUSDTContract } from '../contracts';
import { publicClient } from '../config/wagmi';

// Define the package details interface based on the contract structure
interface PackageDetails {
  entryUSDT: bigint;
  priceBps: number;
  vestSplitBps: number;
  referralRateBps: number;
  exists: boolean;
}

export const usePackageManager = () => {
  const { address } = useAccount();
  const { writeContractAsync, isPending, isSuccess, error } = useWriteContract();

  // Get available packages
  const { data: packageCount } = useReadContract({
    ...getPackageManagerContract(),
    functionName: 'getPackageCount',
  });

  /**
   * Get package details from the smart contract
   * Note: vestSplitBps represents the percentage of tokens allocated to vesting (e.g., 5000 = 50%)
   * This is NOT the vesting duration - all packages use a 5-year vesting period as defined in VestingVault
   */
  const getPackageDetails = async (packageId: number): Promise<PackageDetails> => {
    const contract = getPackageManagerContract();
    if (!publicClient) {
      throw new Error('Public client is not initialized');
    }
    
    const result = await publicClient.readContract({
      address: contract.address,
      abi: contract.abi,
      functionName: 'getPackage',
      args: [packageId],
    });
    
    // Type assertion for the result
    return result as PackageDetails;
  };
  
  // Purchase a package with proper approve flow
  const purchasePackage = async (packageId: number, referrerAddress = '******************************************') => {
    if (!address) throw new Error('Wallet not connected');

    // 1. Get package details
    const packageDetails = await getPackageDetails(packageId);
    const entryAmount = packageDetails.entryUSDT;

    // 2. Always approve USDT first (simpler approach for now)
    // In production, you might want to check allowance first
    await writeContractAsync({
      ...getUSDTContract(),
      functionName: 'approve',
      args: [getPackageManagerContract().address, entryAmount],
    });

    // 3. Purchase the package
    return writeContractAsync({
      ...getPackageManagerContract(),
      functionName: 'purchase',
      args: [packageId, referrerAddress],
    });
  };
  
  return {
    packageCount: packageCount || 0,
    getPackageDetails,
    purchasePackage,
    isPending,
    isSuccess,
    error,
    address
  };
};
