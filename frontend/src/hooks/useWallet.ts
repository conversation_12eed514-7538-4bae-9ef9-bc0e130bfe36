import { useAccount, useDisconnect, useBalance } from 'wagmi';
import { useAppKit } from '@reown/appkit/react';
import { User } from '../types';
import { useReadContract } from 'wagmi';
import { getShareTokenContract, getVestingVaultContract } from '../contracts';
import { formatEther } from 'viem';

export const useWallet = () => {
  const { address, isConnected, isConnecting } = useAccount();
  const { disconnect } = useDisconnect();
  const { open } = useAppKit();
  
  // Get balance for the connected account
  const { data: balance } = useBalance({
    address: address,
  });

  // Get share token balance
  const { data: shareBalance } = useReadContract({
    ...getShareTokenContract(),
    functionName: 'balanceOf',
    args: [address],
  });
  
  // Get vesting info
  const { data: vestedAmount } = useReadContract({
    ...getVestingVaultContract(),
    functionName: 'getVestedAmount',
    args: [address],
  });
  const { data: claimableAmount } = useReadContract({
    ...getVestingVaultContract(),
    functionName: 'getClaimableAmount',
    args: [address],
  });
  const user: User | null = isConnected && address ? {
    id: address,
    address: address,
    balance: balance ? parseFloat(balance.formatted) : 0,
    vestedTokens: typeof vestedAmount === 'bigint' ? parseFloat(formatEther(vestedAmount)) : 0,
    totalPurchased: typeof shareBalance === 'bigint' ? parseFloat(formatEther(shareBalance)) : 0,
    referralCode: address.slice(2, 8).toUpperCase(), // Simple referral code from address
    referralCount: 3, // This will come from Smart contract 
    isConnected: true,
  } : null;

  const connectWallet = async () => {
    try {
      await open();
    } catch (error) {
      console.error('Failed to connect wallet:', error);
      throw error;
    }
  };

  const disconnectWallet = () => {
    disconnect();
  };

  return {
    user,
    isConnecting,
    error: null, // Appkit handles errors internally
    connectWallet,
    disconnectWallet,
    isConnected,
    address,
    balance: balance?.formatted || '0',
    claimableAmount: typeof claimableAmount === 'bigint' ? formatEther(claimableAmount) : '0',
  };
};
