# Test Execution Examples

This document provides examples of how to run the comprehensive AdminPage tests and what to expect from each test category.

## Quick Start

```bash
# Install dependencies (if not already installed)
npm install

# Run all AdminPage tests
npm run test:admin

# Run with coverage
npm run test:admin-coverage

# Run in watch mode for development
npm run test:admin-watch
```

## Test Categories Overview

### 1. AddPackageModal Unit Tests

**File**: `src/components/admin/__tests__/AddPackageModal.test.tsx`

**What it tests**:
- Form validation for all input fields
- Form submission with valid/invalid data
- Modal open/close functionality
- Loading states during submission
- Form reset after successful submission
- Package preview updates

**Example execution**:
```bash
npm run test:unit
# or specifically:
npx vitest src/components/admin/__tests__/AddPackageModal.test.tsx
```

**Expected output**:
```
✓ AddPackageModal > Rendering > should render modal when isOpen is true
✓ AddPackageModal > Form Validation > should show error for empty package ID
✓ AddPackageModal > Form Submission > should submit form with valid data
✓ AddPackageModal > Loading States > should disable form inputs when loading
✓ AddPackageModal > Modal Functionality > should call onClose when cancel button is clicked
```

### 2. Smart Contract Integration Tests

**File**: `src/pages/__tests__/AdminPage.integration.test.tsx`

**What it tests**:
- `addNewPackage` function with proper parameter conversion
- `togglePackageStatus` function for enable/disable operations
- Contract call error handling
- Loading states during contract interactions
- Success/failure response handling

**Example execution**:
```bash
npm run test:integration
# or specifically:
npx vitest src/pages/__tests__/AdminPage.integration.test.tsx
```

**Expected output**:
```
✓ AdminPage Smart Contract Integration > addNewPackage Function > should call writeContractAsync with correct parameters
✓ AdminPage Smart Contract Integration > addNewPackage Function > should convert parameters correctly for smart contract
✓ AdminPage Smart Contract Integration > togglePackageStatus Function > should call disablePackage for active package
✓ AdminPage Smart Contract Integration > Error Handling > should handle network errors gracefully
```

### 3. Component Integration Tests

**File**: `src/pages/__tests__/AdminPage.test.tsx`

**What it tests**:
- Component rendering and navigation
- Package list display and loading states
- Button interactions and state management
- Error handling and recovery
- Responsive design elements

**Example execution**:
```bash
npx vitest src/pages/__tests__/AdminPage.test.tsx
```

**Expected output**:
```
✓ AdminPage Component Integration > Component Rendering > should render admin page with default overview tab
✓ AdminPage Component Integration > Tab Navigation > should switch to packages tab when clicked
✓ AdminPage Component Integration > Add New Package Modal Integration > should open modal when button is clicked
✓ AdminPage Component Integration > Package List Display > should display packages when loaded
```

## Detailed Test Scenarios

### Form Validation Tests

```typescript
// Test: Empty package ID validation
it('should show error for empty package ID', async () => {
  render(<AddPackageModal {...defaultProps} />)
  
  const submitButton = screen.getByRole('button', { name: 'Add Package' })
  await user.click(submitButton)
  
  expect(screen.getByTestId('error-package-id')).toHaveTextContent('Package ID is required')
})
```

**What happens**:
1. Modal renders with empty form
2. User clicks submit without filling package ID
3. Validation error appears
4. Form submission is prevented

### Smart Contract Parameter Conversion

```typescript
// Test: Parameter conversion for addPackage
it('should convert parameters correctly for smart contract', async () => {
  // ... test setup
  
  await waitFor(() => {
    const callArgs = mockWriteContractAsync.mock.calls[0][0].args
    
    expect(callArgs[0]).toBe(BigInt(validPackageData.id))           // ID as BigInt
    expect(callArgs[2]).toBe(parseInt(validPackageData.priceBps) * 100)  // Basis points conversion
  })
})
```

**What happens**:
1. Form data is submitted: `{ id: '1', priceBps: '12', ... }`
2. Parameters are converted: `[BigInt(1), ..., 1200, ...]`
3. Contract function is called with correct types
4. Test verifies the conversion is accurate

### Error Handling Tests

```typescript
// Test: Contract call failure
it('should handle contract call failure', async () => {
  mockWriteContractAsync.mockRejectedValue(new Error('Contract call failed'))
  
  // ... user interaction
  
  await waitFor(() => {
    expect(global.alert).toHaveBeenCalledWith('Failed to add package. Please try again.')
  })
})
```

**What happens**:
1. Mock contract call is set to fail
2. User submits form
3. Contract call throws error
4. Error is caught and user-friendly message is shown
5. Modal remains open for retry

## Coverage Reports

### Running Coverage

```bash
# Generate coverage report
npm run test:coverage

# Open coverage report in browser
open coverage/index.html
```

### Expected Coverage Metrics

```
File                           | % Stmts | % Branch | % Funcs | % Lines
-------------------------------|---------|----------|---------|--------
src/pages/AdminPage.tsx        |   95.2  |   88.9   |   100   |   94.7
src/components/admin/          |   92.1  |   85.7   |   95.8  |   91.3
AddPackageModal.tsx            |         |          |         |
-------------------------------|---------|----------|---------|--------
All files                      |   93.6  |   87.3   |   97.9  |   93.0
```

### Coverage Details

- **Statements**: Code execution coverage
- **Branches**: Conditional logic coverage (if/else, ternary operators)
- **Functions**: Function call coverage
- **Lines**: Line-by-line coverage

## Common Test Patterns

### 1. Async Operation Testing

```typescript
// Pattern: Testing loading states
it('should show loading state during contract call', async () => {
  const { promise, resolve } = createLoadingPromise()
  mockWriteContractAsync.mockReturnValue(promise)
  
  // Trigger action
  await user.click(submitButton)
  
  // Assert loading state
  expect(screen.getByTestId('modal-loading')).toHaveTextContent('loading')
  
  // Resolve and assert completion
  resolve({ hash: '0xmockhash' })
  await waitFor(() => {
    expect(screen.getByTestId('modal-loading')).toHaveTextContent('not-loading')
  })
})
```

### 2. Form Validation Testing

```typescript
// Pattern: Testing validation rules
const validationTests = [
  { field: 'id', value: '', error: 'Package ID is required' },
  { field: 'id', value: '0', error: 'Package ID must be greater than 0' },
  { field: 'priceBps', value: '101', error: 'Price must be between 0 and 100%' },
]

validationTests.forEach(({ field, value, error }) => {
  it(`should validate ${field} field`, async () => {
    // Fill form with invalid value
    await user.type(screen.getByTestId(`input-${field}`), value)
    await user.click(screen.getByRole('button', { name: 'Add Package' }))
    
    // Assert error message
    expect(screen.getByTestId(`error-${field}`)).toHaveTextContent(error)
  })
})
```

### 3. Contract Call Testing

```typescript
// Pattern: Testing contract interactions
it('should call contract with correct parameters', async () => {
  // Setup form data
  const formData = { id: '1', entryAmount: '1000', ... }
  
  // Fill and submit form
  await fillForm(formData)
  await user.click(submitButton)
  
  // Assert contract call
  expect(mockWriteContractAsync).toHaveBeenCalledWith({
    ...mockGetPackageManagerContract(),
    functionName: 'addPackage',
    args: [
      BigInt(formData.id),
      parseUnits(formData.entryAmount, 18),
      parseInt(formData.priceBps) * 100,
      // ... other converted parameters
    ],
  })
})
```

## Debugging Failed Tests

### Common Issues and Solutions

1. **Mock not working**:
   ```bash
   # Check mock setup
   console.log(mockWriteContractAsync.mock.calls)
   ```

2. **Async timing issues**:
   ```typescript
   // Use waitFor for async operations
   await waitFor(() => {
     expect(screen.getByText('Expected text')).toBeInTheDocument()
   })
   ```

3. **Element not found**:
   ```typescript
   // Debug what's rendered
   screen.debug()
   // or check specific element
   console.log(screen.getByTestId('element-id'))
   ```

### Verbose Test Output

```bash
# Run with detailed output
npx vitest --reporter=verbose src/components/admin/__tests__/AddPackageModal.test.tsx
```

This will show:
- Individual test execution times
- Detailed error messages
- Mock call information
- Component render details

## Best Practices Demonstrated

1. **Test Isolation**: Each test resets mocks and state
2. **Realistic Data**: Test data matches actual contract requirements
3. **Error Scenarios**: Both success and failure paths are tested
4. **User Interactions**: Tests simulate real user behavior
5. **Accessibility**: Tests include accessibility checks
6. **Performance**: Tests verify loading states and async operations

## Next Steps

After running these tests successfully:

1. **Add more edge cases** based on your specific requirements
2. **Integrate with CI/CD** pipeline for automated testing
3. **Monitor coverage** and aim for 90%+ coverage
4. **Add visual regression tests** if needed
5. **Performance testing** for large package lists

## Troubleshooting

If tests fail:

1. Check the error message carefully
2. Verify mock setup in `mocks/wagmi.ts`
3. Ensure test data matches contract expectations
4. Check async operation handling
5. Verify component props and state management

For additional help, refer to the main README.md in the test directory.
