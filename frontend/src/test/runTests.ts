#!/usr/bin/env node

/**
 * Test runner script for AdminPage smart contract integration tests
 * 
 * This script provides different test execution modes:
 * - Unit tests only
 * - Integration tests only
 * - All tests
 * - Watch mode
 * - Coverage mode
 */

import { execSync } from 'child_process'
import { existsSync } from 'fs'
import path from 'path'

// Test configuration
const TEST_CONFIG = {
  // Test file patterns
  patterns: {
    unit: 'src/**/*.test.{ts,tsx}',
    integration: 'src/**/*.integration.test.{ts,tsx}',
    all: 'src/**/*.{test,spec}.{ts,tsx}',
  },
  
  // Vitest configuration
  configFile: 'vite.config.test.ts',
  
  // Coverage settings
  coverage: {
    threshold: {
      statements: 80,
      branches: 70,
      functions: 80,
      lines: 80,
    },
    exclude: [
      'src/test/**',
      'src/**/*.test.{ts,tsx}',
      'src/**/*.spec.{ts,tsx}',
      'src/types/**',
    ],
  },
}

// Command line argument parsing
const args = process.argv.slice(2)
const mode = args[0] || 'all'
const flags = args.slice(1)

// Available test modes
const MODES = {
  unit: 'Run unit tests only',
  integration: 'Run integration tests only',
  all: 'Run all tests',
  watch: 'Run tests in watch mode',
  coverage: 'Run tests with coverage report',
  'coverage-unit': 'Run unit tests with coverage',
  'coverage-integration': 'Run integration tests with coverage',
}

// Helper functions
const log = (message: string, type: 'info' | 'success' | 'error' | 'warn' = 'info') => {
  const colors = {
    info: '\x1b[36m',    // cyan
    success: '\x1b[32m', // green
    error: '\x1b[31m',   // red
    warn: '\x1b[33m',    // yellow
  }
  const reset = '\x1b[0m'
  console.log(`${colors[type]}${message}${reset}`)
}

const checkPrerequisites = () => {
  log('Checking prerequisites...', 'info')
  
  // Check if vitest is installed
  try {
    execSync('npx vitest --version', { stdio: 'pipe' })
    log('✓ Vitest is available', 'success')
  } catch (error) {
    log('✗ Vitest is not installed. Run: npm install --save-dev vitest', 'error')
    process.exit(1)
  }
  
  // Check if test config exists
  if (!existsSync(TEST_CONFIG.configFile)) {
    log(`✗ Test config file not found: ${TEST_CONFIG.configFile}`, 'error')
    process.exit(1)
  }
  log('✓ Test configuration found', 'success')
  
  // Check if test files exist
  const testDirs = ['src/components/admin/__tests__', 'src/pages/__tests__', 'src/test']
  const missingDirs = testDirs.filter(dir => !existsSync(dir))
  
  if (missingDirs.length > 0) {
    log(`✗ Missing test directories: ${missingDirs.join(', ')}`, 'error')
    process.exit(1)
  }
  log('✓ Test directories found', 'success')
}

const buildCommand = (testMode: string, additionalFlags: string[] = []) => {
  const baseCmd = 'npx vitest'
  const configFlag = `--config ${TEST_CONFIG.configFile}`
  
  let pattern = ''
  let runFlags = ''
  
  switch (testMode) {
    case 'unit':
      pattern = TEST_CONFIG.patterns.unit
      runFlags = '--run'
      break
      
    case 'integration':
      pattern = TEST_CONFIG.patterns.integration
      runFlags = '--run'
      break
      
    case 'all':
      pattern = TEST_CONFIG.patterns.all
      runFlags = '--run'
      break
      
    case 'watch':
      pattern = TEST_CONFIG.patterns.all
      runFlags = '--watch'
      break
      
    case 'coverage':
    case 'coverage-unit':
    case 'coverage-integration':
      const coveragePattern = testMode === 'coverage-unit' ? TEST_CONFIG.patterns.unit :
                             testMode === 'coverage-integration' ? TEST_CONFIG.patterns.integration :
                             TEST_CONFIG.patterns.all
      pattern = coveragePattern
      runFlags = '--run --coverage'
      break
      
    default:
      log(`Unknown test mode: ${testMode}`, 'error')
      showHelp()
      process.exit(1)
  }
  
  const allFlags = [configFlag, runFlags, ...additionalFlags].filter(Boolean).join(' ')
  return `${baseCmd} ${allFlags} "${pattern}"`
}

const showHelp = () => {
  log('\nAdminPage Test Runner', 'info')
  log('====================', 'info')
  log('\nUsage: npm run test:admin [mode] [flags]', 'info')
  log('\nAvailable modes:', 'info')
  
  Object.entries(MODES).forEach(([mode, description]) => {
    log(`  ${mode.padEnd(20)} ${description}`, 'info')
  })
  
  log('\nExamples:', 'info')
  log('  npm run test:admin unit              # Run unit tests only', 'info')
  log('  npm run test:admin integration       # Run integration tests only', 'info')
  log('  npm run test:admin coverage          # Run all tests with coverage', 'info')
  log('  npm run test:admin watch             # Run tests in watch mode', 'info')
  log('  npm run test:admin all --reporter=verbose  # Run all tests with verbose output', 'info')
  
  log('\nTest Files:', 'info')
  log('  Unit Tests:        src/components/admin/__tests__/*.test.tsx', 'info')
  log('  Integration Tests: src/pages/__tests__/*.integration.test.tsx', 'info')
  log('  Component Tests:   src/pages/__tests__/*.test.tsx', 'info')
}

const runTests = async () => {
  if (args.includes('--help') || args.includes('-h')) {
    showHelp()
    return
  }
  
  if (!Object.keys(MODES).includes(mode)) {
    log(`Invalid mode: ${mode}`, 'error')
    showHelp()
    process.exit(1)
  }
  
  log(`\nRunning AdminPage tests in ${mode} mode...`, 'info')
  log('=' .repeat(50), 'info')
  
  checkPrerequisites()
  
  const command = buildCommand(mode, flags)
  log(`\nExecuting: ${command}`, 'info')
  
  try {
    execSync(command, { 
      stdio: 'inherit',
      cwd: process.cwd(),
    })
    
    log('\n✓ Tests completed successfully!', 'success')
    
    if (mode.includes('coverage')) {
      log('\nCoverage report generated in coverage/ directory', 'info')
      log('Open coverage/index.html in your browser to view detailed report', 'info')
    }
    
  } catch (error) {
    log('\n✗ Tests failed!', 'error')
    process.exit(1)
  }
}

// Main execution
if (require.main === module) {
  runTests().catch((error) => {
    log(`Unexpected error: ${error.message}`, 'error')
    process.exit(1)
  })
}

export { runTests, TEST_CONFIG, MODES }
