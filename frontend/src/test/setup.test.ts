import { beforeAll, afterAll, beforeEach, afterEach, vi } from 'vitest'
import '@testing-library/jest-dom'
import React from 'react'

// Global test setup for AdminPage and smart contract integration tests

// Mock global objects and methods
beforeAll(() => {
  // Mock window.location
  Object.defineProperty(window, 'location', {
    value: {
      reload: vi.fn(),
      href: 'http://localhost:3000',
      pathname: '/',
      search: '',
      hash: '',
    },
    writable: true,
  })

  // Mock global alert and confirm
  global.alert = vi.fn()
  global.confirm = vi.fn(() => true)

  // Mock console methods to reduce noise in tests
  global.console = {
    ...console,
    log: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
    info: vi.fn(),
  }

  // Mock localStorage
  const localStorageMock = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
  }
  Object.defineProperty(window, 'localStorage', {
    value: localStorageMock,
  })

  // Mock sessionStorage
  Object.defineProperty(window, 'sessionStorage', {
    value: localStorageMock,
  })

  // Mock matchMedia for responsive design tests
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation(query => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: vi.fn(), // deprecated
      removeListener: vi.fn(), // deprecated
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  })

  // Mock ResizeObserver
  global.ResizeObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  }))

  // Mock IntersectionObserver
  global.IntersectionObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  }))
})

// Reset mocks before each test
beforeEach(() => {
  vi.clearAllMocks()
  
  // Reset window.location.reload
  vi.mocked(window.location.reload).mockClear()
  
  // Reset global functions
  vi.mocked(global.alert).mockClear()
  vi.mocked(global.confirm).mockClear()
  
  // Reset console mocks
  vi.mocked(console.log).mockClear()
  vi.mocked(console.error).mockClear()
  vi.mocked(console.warn).mockClear()
  vi.mocked(console.info).mockClear()
})

// Cleanup after each test
afterEach(() => {
  // Clean up any timers
  vi.runOnlyPendingTimers()
  vi.useRealTimers()
})

// Global cleanup
afterAll(() => {
  vi.restoreAllMocks()
})

// Mock wagmi hooks globally
vi.mock('wagmi', async () => {
  const actual = await vi.importActual('wagmi')
  return {
    ...actual,
    useWriteContract: vi.fn(() => ({
      writeContractAsync: vi.fn().mockResolvedValue({ hash: '0xmockhash' }),
      isPending: false,
      isSuccess: false,
      error: null,
    })),
    useReadContract: vi.fn(() => ({
      data: undefined,
      isLoading: false,
      error: null,
    })),
    useAccount: vi.fn(() => ({
      address: '0x1234567890123456789012345678901234567890',
      isConnected: true,
    })),
  }
})

// Mock usePackageManager hook globally
vi.mock('../hooks/usePackageManager', () => ({
  usePackageManager: vi.fn(() => ({
    packageCount: 0,
    getPackageDetails: vi.fn(),
    purchasePackage: vi.fn(),
    isPending: false,
    isSuccess: false,
    error: null,
  })),
}))

// Mock contract configuration globally
vi.mock('../contracts', () => ({
  getPackageManagerContract: vi.fn(() => ({
    address: '0x1234567890123456789012345678901234567890' as `0x${string}`,
    abi: [],
  })),
  getUSDTContract: vi.fn(() => ({
    address: '0x0987654321098765432109876543210987654321' as `0x${string}`,
    abi: [],
  })),
}))

// Mock viem utilities globally
vi.mock('viem', async () => {
  const actual = await vi.importActual('viem')
  return {
    ...actual,
    parseUnits: vi.fn((value: string, decimals: number) => {
      const numValue = parseFloat(value)
      const multiplier = Math.pow(10, decimals)
      return BigInt(Math.floor(numValue * multiplier))
    }),
    formatUnits: vi.fn((value: bigint, decimals: number) => {
      const divisor = BigInt(Math.pow(10, decimals))
      return (Number(value) / Number(divisor)).toString()
    }),
    getContract: vi.fn(() => ({
      read: {
        getPackage: vi.fn(),
        getPackageCount: vi.fn(),
      },
      write: {
        addPackage: vi.fn(),
        enablePackage: vi.fn(),
        disablePackage: vi.fn(),
      },
    })),
  }
})

// Mock framer-motion globally
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
    span: ({ children, ...props }: any) => <span {...props}>{children}</span>,
  },
  AnimatePresence: ({ children }: any) => children,
}))

// Mock lucide-react icons globally
vi.mock('lucide-react', () => ({
  Users: () => <div data-testid="users-icon" />,
  DollarSign: () => <div data-testid="dollar-sign-icon" />,
  TrendingUp: () => <div data-testid="trending-up-icon" />,
  Settings: () => <div data-testid="settings-icon" />,
  Shield: () => <div data-testid="shield-icon" />,
  BarChart3: () => <div data-testid="bar-chart-icon" />,
  Package: () => <div data-testid="package-icon" />,
  AlertTriangle: () => <div data-testid="alert-triangle-icon" />,
  Plus: () => <div data-testid="plus-icon" />,
  Edit: () => <div data-testid="edit-icon" />,
  Trash2: () => <div data-testid="trash-icon" />,
  Eye: () => <div data-testid="eye-icon" />,
  EyeOff: () => <div data-testid="eye-off-icon" />,
  Check: () => <div data-testid="check-icon" />,
  X: () => <div data-testid="x-icon" />,
  Loader2: () => <div data-testid="loader-icon" />,
}))

// Export test utilities
export const testUtils = {
  // Helper to create mock contract responses
  createMockContractResponse: (overrides = {}) => ({
    entryUSDT: BigInt('1000000000000000000000'),
    priceBps: 1200,
    vestSplitBps: 5000,
    referralRateBps: 500,
    active: true,
    exists: true,
    ...overrides,
  }),

  // Helper to create mock form data
  createMockFormData: (overrides = {}) => ({
    id: '1',
    entryAmount: '1000',
    priceBps: '12',
    vestSplitBps: '50',
    referralRateBps: '5',
    ...overrides,
  }),

  // Helper to create mock errors
  createMockError: (type: 'network' | 'user-rejection' | 'contract' = 'network') => {
    const errors = {
      network: Object.assign(new Error('Network error'), { name: 'NetworkError' }),
      'user-rejection': Object.assign(new Error('User rejected'), { name: 'UserRejectedRequestError' }),
      contract: Object.assign(new Error('Contract error'), { name: 'ContractExecutionError' }),
    }
    return errors[type]
  },

  // Helper to wait for async operations
  waitForAsync: () => new Promise(resolve => setTimeout(resolve, 0)),

  // Helper to create loading promises
  createLoadingPromise: () => {
    let resolvePromise: (value: any) => void
    let rejectPromise: (error: any) => void
    
    const promise = new Promise((resolve, reject) => {
      resolvePromise = resolve
      rejectPromise = reject
    })
    
    return {
      promise,
      resolve: resolvePromise!,
      reject: rejectPromise!,
    }
  },
}
