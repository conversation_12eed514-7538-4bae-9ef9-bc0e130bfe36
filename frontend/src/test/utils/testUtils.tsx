import React from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { vi } from 'vitest'

// Test utilities for AdminPage and related components

/**
 * Custom render function that includes common providers and setup
 */
export const renderWithProviders = (
  ui: React.ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => {
  const Wrapper = ({ children }: { children: React.ReactNode }) => {
    return <>{children}</>
  }

  return render(ui, { wrapper: Wrapper, ...options })
}

/**
 * Mock implementations for common contract interactions
 */
export const createMockContractResponse = (overrides = {}) => ({
  entryUSDT: BigInt('1000000000000000000000'), // 1000 USDT
  priceBps: 1200, // 12% APY
  vestSplitBps: 5000, // 50% vesting
  referralRateBps: 500, // 5% referral
  active: true,
  exists: true,
  ...overrides,
})

/**
 * Mock package data generator
 */
export const createMockPackage = (id: number, overrides = {}) => ({
  id,
  name: `Package ${id + 1}`,
  minAmount: 1000,
  apy: 12,
  vesting: 50,
  active: true,
  referralRate: 5,
  ...overrides,
})

/**
 * Mock form data for testing
 */
export const createMockFormData = (overrides = {}) => ({
  id: '1',
  entryAmount: '1000',
  priceBps: '12',
  vestSplitBps: '50',
  referralRateBps: '5',
  ...overrides,
})

/**
 * Helper to wait for async operations in tests
 */
export const waitForAsyncOperation = () => new Promise(resolve => setTimeout(resolve, 0))

/**
 * Mock contract call expectations
 */
export const expectContractCall = (mockFn: any, functionName: string, args: any[]) => {
  expect(mockFn).toHaveBeenCalledWith(
    expect.objectContaining({
      functionName,
      args,
    })
  )
}

/**
 * Mock error scenarios
 */
export const createMockError = (type: 'network' | 'user-rejection' | 'contract' | 'generic' = 'generic') => {
  const errors = {
    network: Object.assign(new Error('Network error'), { name: 'NetworkError' }),
    'user-rejection': Object.assign(new Error('User rejected transaction'), { name: 'UserRejectedRequestError' }),
    contract: Object.assign(new Error('Contract execution reverted'), { name: 'ContractExecutionError' }),
    generic: new Error('Something went wrong'),
  }
  
  return errors[type]
}

/**
 * Helper to simulate loading states
 */
export const createLoadingPromise = () => {
  let resolvePromise: (value: any) => void
  let rejectPromise: (error: any) => void
  
  const promise = new Promise((resolve, reject) => {
    resolvePromise = resolve
    rejectPromise = reject
  })
  
  return {
    promise,
    resolve: resolvePromise!,
    reject: rejectPromise!,
  }
}

/**
 * Mock localStorage for tests
 */
export const mockLocalStorage = () => {
  const store: Record<string, string> = {}
  
  return {
    getItem: vi.fn((key: string) => store[key] || null),
    setItem: vi.fn((key: string, value: string) => {
      store[key] = value
    }),
    removeItem: vi.fn((key: string) => {
      delete store[key]
    }),
    clear: vi.fn(() => {
      Object.keys(store).forEach(key => delete store[key])
    }),
  }
}

/**
 * Mock window methods commonly used in tests
 */
export const mockWindowMethods = () => {
  Object.defineProperty(window, 'location', {
    value: {
      reload: vi.fn(),
      href: 'http://localhost:3000',
      pathname: '/',
    },
    writable: true,
  })
  
  global.alert = vi.fn()
  global.confirm = vi.fn(() => true)
  
  return {
    reload: vi.mocked(window.location.reload),
    alert: vi.mocked(global.alert),
    confirm: vi.mocked(global.confirm),
  }
}

/**
 * Helper to create mock event objects
 */
export const createMockEvent = (overrides = {}) => ({
  preventDefault: vi.fn(),
  stopPropagation: vi.fn(),
  target: { value: '' },
  ...overrides,
})

/**
 * Validation helpers for form testing
 */
export const validationHelpers = {
  isValidPackageId: (id: string) => id !== '' && parseInt(id) > 0,
  isValidEntryAmount: (amount: string) => amount !== '' && parseFloat(amount) > 0,
  isValidPercentage: (percentage: string) => {
    const num = parseFloat(percentage)
    return percentage !== '' && num >= 0 && num <= 100
  },
  isValidBasisPoints: (bps: string) => {
    const num = parseFloat(bps)
    return percentage !== '' && num >= 0 && num <= 10000
  },
}

/**
 * Contract parameter conversion helpers
 */
export const contractHelpers = {
  toBigInt: (value: string | number) => BigInt(value),
  toBasisPoints: (percentage: string | number) => parseInt(percentage.toString()) * 100,
  toWei: (amount: string | number, decimals = 18) => {
    const numValue = parseFloat(amount.toString())
    const multiplier = Math.pow(10, decimals)
    return BigInt(Math.floor(numValue * multiplier))
  },
  fromWei: (amount: bigint, decimals = 18) => {
    const divisor = BigInt(Math.pow(10, decimals))
    return (Number(amount) / Number(divisor)).toString()
  },
}

/**
 * Test data generators
 */
export const testDataGenerators = {
  generatePackages: (count: number) => 
    Array.from({ length: count }, (_, i) => createMockPackage(i)),
  
  generateFormErrors: (fields: string[]) => 
    fields.reduce((acc, field) => ({ ...acc, [field]: `${field} is required` }), {}),
  
  generateContractArgs: (formData: any) => [
    BigInt(formData.id),
    contractHelpers.toWei(formData.entryAmount),
    contractHelpers.toBasisPoints(formData.priceBps),
    contractHelpers.toBasisPoints(formData.vestSplitBps),
    contractHelpers.toBasisPoints(formData.referralRateBps),
  ],
}

/**
 * Assertion helpers
 */
export const assertionHelpers = {
  expectFormToBeReset: (screen: any) => {
    expect(screen.getByTestId('input-package-id')).toHaveValue('')
    expect(screen.getByTestId('input-entry-amount--usdt-')).toHaveValue('')
    expect(screen.getByTestId('input-price--apy--')).toHaveValue('')
    expect(screen.getByTestId('input-vesting-split--')).toHaveValue('')
    expect(screen.getByTestId('input-referral-rate--')).toHaveValue('')
  },
  
  expectLoadingState: (screen: any, isLoading: boolean) => {
    const loadingElements = screen.queryAllByText('Loading...')
    if (isLoading) {
      expect(loadingElements.length).toBeGreaterThan(0)
    } else {
      expect(loadingElements.length).toBe(0)
    }
  },
  
  expectErrorMessage: (screen: any, fieldName: string, message: string) => {
    const errorElement = screen.getByTestId(`error-${fieldName}`)
    expect(errorElement).toHaveTextContent(message)
    expect(errorElement).toHaveAttribute('role', 'alert')
  },
}

// Re-export testing library utilities
export * from '@testing-library/react'
export { userEvent } from '@testing-library/user-event'
