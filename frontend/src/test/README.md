# AdminPage Smart Contract Integration Tests

This directory contains comprehensive tests for the AdminPage component and its smart contract integration functionality.

## Test Structure

```
src/test/
├── README.md                           # This file
├── setup.ts                           # Global test setup (existing)
├── setup.test.ts                      # Enhanced test setup with mocks
├── runTests.ts                        # Test runner script
├── testData.ts                        # Test data (existing)
├── mocks/
│   └── wagmi.ts                       # Wagmi and contract mocks
└── utils/
    └── testUtils.tsx                  # Test utilities and helpers

src/components/admin/__tests__/
└── AddPackageModal.test.tsx           # Unit tests for AddPackageModal

src/pages/__tests__/
├── AdminPage.test.tsx                 # Component integration tests
└── AdminPage.integration.test.tsx     # Smart contract integration tests
```

## Test Categories

### 1. Unit Tests (`AddPackageModal.test.tsx`)

Tests the AddPackageModal component in isolation:

- **Form Validation**: Required fields, numeric ranges, error states
- **Form Submission**: Valid data handling, form reset, error handling
- **Loading States**: Disabled inputs, loading buttons, modal behavior
- **Modal Functionality**: Open/close, reset on close
- **Package Preview**: Real-time updates, default values
- **Input Attributes**: Correct types, step values, accessibility

### 2. Integration Tests (`AdminPage.integration.test.tsx`)

Tests smart contract function integration:

- **addNewPackage Function**:
  - Correct parameter conversion (BigInt for ID, parseUnits for amounts, basis points)
  - Contract call with proper arguments
  - Success handling (modal close, page reload)
  - Error handling with user feedback
  - Loading states during contract calls

- **togglePackageStatus Function**:
  - Enable/disable package operations
  - Correct function selection based on current status
  - Parameter conversion (BigInt for package ID)
  - Success and error handling
  - Loading states

### 3. Component Integration Tests (`AdminPage.test.tsx`)

Tests component interactions and UI behavior:

- **Component Rendering**: Tabs, stats, package lists
- **Tab Navigation**: Switching between overview, packages, settings
- **Modal Integration**: Opening/closing, data flow
- **Package Display**: Loading states, package details, status indicators
- **Action Buttons**: Edit, enable/disable functionality
- **Error Handling**: Graceful degradation, error messages
- **Responsive Design**: Grid layouts, mobile compatibility

## Running Tests

### Prerequisites

```bash
# Install dependencies
npm install

# Ensure vitest is installed
npm install --save-dev vitest @testing-library/react @testing-library/jest-dom @testing-library/user-event
```

### Test Commands

```bash
# Run all tests
npm run test

# Run specific test categories
npm run test:unit                    # Unit tests only
npm run test:integration            # Integration tests only
npm run test:admin                  # All AdminPage tests

# Run tests with coverage
npm run test:coverage               # All tests with coverage
npm run test:coverage-unit          # Unit tests with coverage
npm run test:coverage-integration   # Integration tests with coverage

# Run tests in watch mode
npm run test:watch                  # Watch mode for development

# Run specific test files
npx vitest src/components/admin/__tests__/AddPackageModal.test.tsx
npx vitest src/pages/__tests__/AdminPage.integration.test.tsx
```

### Custom Test Runner

Use the custom test runner for more control:

```bash
# Using the test runner script
node src/test/runTests.ts unit
node src/test/runTests.ts integration
node src/test/runTests.ts coverage
node src/test/runTests.ts watch
```

## Test Data

### Valid Package Data
```typescript
{
  id: '1',
  entryAmount: '1000',
  priceBps: '12',        // 12% APY
  vestSplitBps: '50',    // 50% vesting
  referralRateBps: '5',  // 5% referral rate
}
```

### Expected Contract Arguments
```typescript
[
  BigInt('1'),                           // Package ID
  BigInt('1000000000000000000000'),     // Entry amount in wei (1000 USDT)
  1200,                                 // Price in basis points (12% * 100)
  5000,                                 // Vest split in basis points (50% * 100)
  500,                                  // Referral rate in basis points (5% * 100)
]
```

## Mock Configuration

### Wagmi Hooks
- `useWriteContract`: Mocked with success/failure scenarios
- `usePackageManager`: Mocked package data and functions
- Contract configuration: Mocked addresses and ABIs

### Viem Utilities
- `parseUnits`: Converts string amounts to BigInt with decimals
- `formatUnits`: Converts BigInt to readable string format

### Contract Functions
- `addPackage`: Mocked smart contract function
- `enablePackage`/`disablePackage`: Mocked toggle functions

## Test Scenarios

### Positive Test Cases
- ✅ Valid form submission with correct parameter conversion
- ✅ Successful contract calls with proper response handling
- ✅ Modal state management (open/close/reset)
- ✅ Loading states during async operations
- ✅ Package list display and updates

### Negative Test Cases
- ❌ Form validation errors (empty fields, invalid ranges)
- ❌ Contract call failures (network errors, user rejection)
- ❌ Package fetching errors
- ❌ Invalid package data handling

### Edge Cases
- 🔄 Loading states and disabled interactions
- 🔄 Form reset after successful submission
- 🔄 Error recovery and retry scenarios
- 🔄 Package status toggle for active/inactive packages

## Coverage Targets

- **Statements**: 90%+
- **Branches**: 85%+
- **Functions**: 90%+
- **Lines**: 90%+

## Debugging Tests

### Common Issues

1. **Mock not working**: Ensure mocks are imported before the component
2. **Async operations**: Use `waitFor` for async state changes
3. **Form validation**: Check exact error message text
4. **Contract calls**: Verify parameter conversion and function names

### Debug Commands

```bash
# Run tests with verbose output
npx vitest --reporter=verbose

# Run single test with debug info
npx vitest --reporter=verbose src/components/admin/__tests__/AddPackageModal.test.tsx

# Run tests with coverage and open report
npx vitest --coverage && open coverage/index.html
```

### Test Utilities

The `testUtils.tsx` file provides helpful utilities:

- `renderWithProviders`: Custom render with providers
- `createMockContractResponse`: Generate mock contract data
- `expectContractCall`: Assert contract function calls
- `createMockError`: Generate different error types
- `assertionHelpers`: Common test assertions

## Best Practices

1. **Isolation**: Each test should be independent
2. **Mocking**: Mock external dependencies (wagmi, contracts)
3. **Assertions**: Use specific, meaningful assertions
4. **Error Handling**: Test both success and failure scenarios
5. **Loading States**: Test async operation states
6. **Accessibility**: Include accessibility checks where relevant
7. **Real Data**: Use realistic test data that matches contract requirements

## Contributing

When adding new tests:

1. Follow the existing test structure
2. Use the provided test utilities
3. Include both positive and negative test cases
4. Update this README if adding new test categories
5. Ensure tests pass in CI/CD pipeline

## Troubleshooting

### Common Test Failures

1. **"Cannot find module"**: Check import paths and mock setup
2. **"Element not found"**: Verify component rendering and test IDs
3. **"Async operation timeout"**: Increase timeout or check async handling
4. **"Mock function not called"**: Verify mock setup and function calls

### Getting Help

- Check the test output for specific error messages
- Review the mock configuration in `mocks/wagmi.ts`
- Ensure test data matches contract requirements
- Verify component props and state management
