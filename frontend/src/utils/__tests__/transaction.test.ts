import { describe, it, expect, vi } from 'vitest';
import { 
  formatTransactionHash, 
  getBlockExplorerInfo, 
  getTransactionUrl, 
  getTransactionDetails 
} from '../transaction';

describe('Transaction Utilities', () => {
  describe('formatTransactionHash', () => {
    it('should format a full transaction hash with default parameters', () => {
      const hash = '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef';
      const formatted = formatTransactionHash(hash);
      expect(formatted).toBe('0x1234...cdef');
    });

    it('should format a transaction hash with custom parameters', () => {
      const hash = '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef';
      const formatted = formatTransactionHash(hash, 8, 6);
      expect(formatted).toBe('0x123456...abcdef');
    });

    it('should return the original hash if it is too short', () => {
      const hash = '0x123';
      const formatted = formatTransactionHash(hash);
      expect(formatted).toBe('0x123');
    });

    it('should handle empty string', () => {
      const formatted = formatTransactionHash('');
      expect(formatted).toBe('');
    });
  });

  describe('getBlockExplorerInfo', () => {
    it('should return BSC Testnet info for chain ID 97', () => {
      const info = getBlockExplorerInfo(97);
      expect(info).toEqual({
        url: 'https://testnet.bscscan.com',
        name: 'BSCScan Testnet'
      });
    });

    it('should return BSC Mainnet info for chain ID 56', () => {
      const info = getBlockExplorerInfo(56);
      expect(info).toEqual({
        url: 'https://bscscan.com',
        name: 'BSCScan'
      });
    });

    it('should return BSC Testnet info for unknown chain ID', () => {
      const info = getBlockExplorerInfo(999);
      expect(info).toEqual({
        url: 'https://testnet.bscscan.com',
        name: 'BSCScan Testnet'
      });
    });
  });

  describe('getTransactionUrl', () => {
    it('should generate correct URL for BSC Testnet', () => {
      const hash = '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef';
      const url = getTransactionUrl(hash, 97);
      expect(url).toBe(`https://testnet.bscscan.com/tx/${hash}`);
    });

    it('should generate correct URL for BSC Mainnet', () => {
      const hash = '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef';
      const url = getTransactionUrl(hash, 56);
      expect(url).toBe(`https://bscscan.com/tx/${hash}`);
    });
  });

  describe('getTransactionDetails', () => {
    it('should return complete transaction details for BSC Testnet', () => {
      const hash = '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef';
      const details = getTransactionDetails(hash, 97);
      
      expect(details).toEqual({
        hash: '0x1234...cdef',
        explorerUrl: `https://testnet.bscscan.com/tx/${hash}`,
        explorerName: 'BSCScan Testnet'
      });
    });

    it('should return complete transaction details for BSC Mainnet', () => {
      const hash = '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef';
      const details = getTransactionDetails(hash, 56);
      
      expect(details).toEqual({
        hash: '0x1234...cdef',
        explorerUrl: `https://bscscan.com/tx/${hash}`,
        explorerName: 'BSCScan'
      });
    });
  });
});
