import { signTypedData } from '@wagmi/core';
import { getContract } from 'viem';
import deployment from '../contracts/deployment.json';
import MinimalForwarderABI from '../contracts/abis/MinimalForwarder.json';
import { publicClient } from '../config/wagmi';

// EIP-712 domain and types
const domain = {
  name: 'MinimalForwarder',
  version: '0.0.1',
  chainId: 97, // BSC Testnet
  verifyingContract: deployment.minimalForwarder as `0x${string}`
};

const ForwardRequestType = [
  { name: 'from', type: 'address' },
  { name: 'to', type: 'address' },
  { name: 'value', type: 'uint256' },
  { name: 'gas', type: 'uint256' },
  { name: 'nonce', type: 'uint256' },
  { name: 'data', type: 'bytes' }
];

export async function signMetaTxRequest(signer, request) {
  const forwarder = getContract({
    address: deployment.minimalForwarder as `0x${string}`,
    abi: MinimalForwarderABI.abi,
    publicClient
  });
  
  // Get the user's nonce
  const nonce = await forwarder.read.getNonce([request.from]);
  