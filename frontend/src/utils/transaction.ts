import { useChainId } from 'wagmi';

/**
 * Transaction hash formatting and blockchain explorer utilities
 */

export interface TransactionDetails {
  hash: string;
  explorerUrl: string;
  explorerName: string;
}

/**
 * Format a transaction hash for display (truncate with ellipsis)
 */
export const formatTransactionHash = (hash: string, startChars = 6, endChars = 4): string => {
  if (!hash || hash.length < startChars + endChars) {
    return hash;
  }
  return `${hash.slice(0, startChars)}...${hash.slice(-endChars)}`;
};

/**
 * Get the appropriate block explorer URL and name based on chain ID
 */
export const getBlockExplorerInfo = (chainId: number): { url: string; name: string } => {
  switch (chainId) {
    case 97: // BSC Testnet
      return {
        url: 'https://testnet.bscscan.com',
        name: 'BSCScan Testnet'
      };
    case 56: // BSC Mainnet
      return {
        url: 'https://bscscan.com',
        name: 'BSCScan'
      };
    default:
      // Default to BSC Testnet if unknown chain
      return {
        url: 'https://testnet.bscscan.com',
        name: 'BSCScan Testnet'
      };
  }
};

/**
 * Generate a complete transaction URL for the block explorer
 */
export const getTransactionUrl = (hash: string, chainId: number): string => {
  const { url } = getBlockExplorerInfo(chainId);
  return `${url}/tx/${hash}`;
};

/**
 * Get complete transaction details including formatted hash and explorer info
 */
export const getTransactionDetails = (hash: string, chainId: number): TransactionDetails => {
  const { url, name } = getBlockExplorerInfo(chainId);
  return {
    hash: formatTransactionHash(hash),
    explorerUrl: getTransactionUrl(hash, chainId),
    explorerName: name
  };
};

/**
 * Hook to get current chain's block explorer info
 */
export const useBlockExplorer = () => {
  const chainId = useChainId();
  
  return {
    getTransactionDetails: (hash: string) => getTransactionDetails(hash, chainId),
    getTransactionUrl: (hash: string) => getTransactionUrl(hash, chainId),
    explorerInfo: getBlockExplorerInfo(chainId)
  };
};
