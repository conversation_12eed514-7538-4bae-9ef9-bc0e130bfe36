import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { PurchasePage } from '../PurchasePage';

// Mock all the hooks and components
vi.mock('../../hooks/useWallet', () => ({
  useWallet: vi.fn(),
}));

vi.mock('../../hooks/usePackageManager', () => ({
  usePackageManager: vi.fn(),
}));

vi.mock('../../hooks/useOnChainPackages', () => ({
  useOnChainPackages: vi.fn(),
}));

vi.mock('../../components/ui/Toast', () => ({
  useToast: vi.fn(),
  ToastProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

vi.mock('../../components/purchase/PackageCard', () => ({
  PackageCard: ({ package: pkg, onSelect, selected }: any) => (
    <div data-testid={`package-card-${pkg.id}`}>
      <h3>{pkg.name}</h3>
      <p>{pkg.description}</p>
      <button 
        onClick={() => onSelect(pkg.id)}
        data-testid={`select-package-${pkg.id}`}
        className={selected ? 'selected' : ''}
      >
        Select {pkg.name}
      </button>
    </div>
  ),
}));

vi.mock('../../components/purchase/PaymentForm', () => ({
  PaymentForm: ({ selectedPackage, onPayment, referralCode, isPending }: any) => (
    <div data-testid="payment-form">
      <h3>Payment for {selectedPackage?.name}</h3>
      <p>Referral: {referralCode || 'None'}</p>
      <button 
        onClick={() => onPayment({ amount: 1000, referralCode })}
        disabled={isPending}
        data-testid="submit-payment"
      >
        {isPending ? 'Processing...' : 'Submit Payment'}
      </button>
    </div>
  ),
}));

import { useWallet } from '../../hooks/useWallet';
import { usePackageManager } from '../../hooks/usePackageManager';
import { useOnChainPackages } from '../../hooks/useOnChainPackages';
import { useToast } from '../../components/ui/Toast';

const mockUseWallet = useWallet as any;
const mockUsePackageManager = usePackageManager as any;
const mockUseOnChainPackages = useOnChainPackages as any;
const mockUseToast = useToast as any;

describe('PurchasePage Integration', () => {
  const mockOnNavigate = vi.fn();
  const mockPurchasePackage = vi.fn();
  const mockToast = {
    success: vi.fn(),
    error: vi.fn(),
    info: vi.fn(),
    warning: vi.fn(),
  };

  const mockPackages = [
    {
      id: '0',
      name: 'Starter Package',
      minAmount: 1000,
      maxAmount: 10000,
      vestingPeriod: 60,
      interestRate: 12,
      description: 'Perfect for beginners',
      features: ['Feature 1', 'Feature 2'],
      entryUSDT: BigInt('1000000000'),
      priceBps: 1200,
      vestSplitBps: 5000,
      referralRateBps: 500,
      active: true,
    },
    {
      id: '1',
      name: 'Growth Package',
      minAmount: 5000,
      maxAmount: 50000,
      vestingPeriod: 60,
      interestRate: 15,
      description: 'Most popular choice',
      features: ['Feature 1', 'Feature 2', 'Feature 3'],
      entryUSDT: BigInt('5000000000'),
      priceBps: 1500,
      vestSplitBps: 6000,
      referralRateBps: 300,
      active: true,
      popular: true,
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup default mocks
    mockUseWallet.mockReturnValue({
      isConnected: true,
    });

    mockUsePackageManager.mockReturnValue({
      purchasePackage: mockPurchasePackage,
      isPending: false,
    });

    mockUseOnChainPackages.mockReturnValue({
      packages: mockPackages,
      loading: false,
      error: null,
    });

    mockUseToast.mockReturnValue(mockToast);
    
    // Mock URL search params
    delete (window as any).location;
    (window as any).location = { search: '' };
  });

  it('should render packages and allow selection', async () => {
    const user = userEvent.setup();
    
    render(<PurchasePage onNavigate={mockOnNavigate} />);

    // Should show packages
    expect(screen.getByText('Starter Package')).toBeInTheDocument();
    expect(screen.getByText('Growth Package')).toBeInTheDocument();

    // Select a package
    await user.click(screen.getByTestId('select-package-0'));

    // Should show selection confirmation
    await waitFor(() => {
      expect(screen.getByText(/Selected: Starter Package/)).toBeInTheDocument();
    });

    // Should show continue button
    expect(screen.getByText('Continue to Payment')).toBeInTheDocument();
  });

  it('should handle complete purchase flow', async () => {
    const user = userEvent.setup();
    mockPurchasePackage.mockResolvedValue({ hash: '0xmockhash' });
    
    render(<PurchasePage onNavigate={mockOnNavigate} />);

    // Select package
    await user.click(screen.getByTestId('select-package-0'));
    
    // Continue to payment
    await user.click(screen.getByText('Continue to Payment'));

    // Should show payment form
    await waitFor(() => {
      expect(screen.getByTestId('payment-form')).toBeInTheDocument();
    });

    // Submit payment
    await user.click(screen.getByTestId('submit-payment'));

    // Should call purchase function
    await waitFor(() => {
      expect(mockPurchasePackage).toHaveBeenCalledWith(0, '0x0000000000000000000000000000000000000000');
    });

    // Should show success toast
    expect(mockToast.success).toHaveBeenCalledWith(
      'Package purchased successfully!',
      'Your investment has been processed'
    );
  });

  it('should handle referral code from URL', async () => {
    const referralCode = '0x1234567890123456789012345678901234567890';
    (window as any).location.search = `?ref=${referralCode}`;
    
    const user = userEvent.setup();
    mockPurchasePackage.mockResolvedValue({ hash: '0xmockhash' });
    
    render(<PurchasePage onNavigate={mockOnNavigate} />);

    // Select package and continue
    await user.click(screen.getByTestId('select-package-0'));
    await user.click(screen.getByText('Continue to Payment'));

    // Should show referral code in payment form
    await waitFor(() => {
      expect(screen.getByText(`Referral: ${referralCode}`)).toBeInTheDocument();
    });

    // Submit payment
    await user.click(screen.getByTestId('submit-payment'));

    // Should use referral code
    await waitFor(() => {
      expect(mockPurchasePackage).toHaveBeenCalledWith(0, referralCode);
    });
  });

  it('should handle purchase errors gracefully', async () => {
    const user = userEvent.setup();
    const error = new Error('Insufficient funds');
    error.message = 'insufficient funds';
    mockPurchasePackage.mockRejectedValue(error);
    
    render(<PurchasePage onNavigate={mockOnNavigate} />);

    // Select package and continue to payment
    await user.click(screen.getByTestId('select-package-0'));
    await user.click(screen.getByText('Continue to Payment'));
    
    // Submit payment
    await user.click(screen.getByTestId('submit-payment'));

    // Should show error toast
    await waitFor(() => {
      expect(mockToast.error).toHaveBeenCalledWith(
        'Insufficient funds',
        'Please ensure you have enough USDT and BNB for gas'
      );
    });
  });

  it('should handle user rejection error', async () => {
    const user = userEvent.setup();
    const error = new Error('User rejected transaction');
    error.name = 'UserRejectedRequestError';
    mockPurchasePackage.mockRejectedValue(error);
    
    render(<PurchasePage onNavigate={mockOnNavigate} />);

    await user.click(screen.getByTestId('select-package-0'));
    await user.click(screen.getByText('Continue to Payment'));
    await user.click(screen.getByTestId('submit-payment'));

    await waitFor(() => {
      expect(mockToast.error).toHaveBeenCalledWith(
        'Transaction cancelled',
        'You cancelled the transaction'
      );
    });
  });

  it('should show loading state during transaction', async () => {
    const user = userEvent.setup();
    mockUsePackageManager.mockReturnValue({
      purchasePackage: mockPurchasePackage,
      isPending: true,
    });
    
    render(<PurchasePage onNavigate={mockOnNavigate} />);

    await user.click(screen.getByTestId('select-package-0'));

    // Button should show processing state
    expect(screen.getByText('Processing…')).toBeInTheDocument();
    expect(screen.getByText('Processing…')).toBeDisabled();
  });

  it('should show wallet connection prompt when not connected', async () => {
    const user = userEvent.setup();
    mockUseWallet.mockReturnValue({
      isConnected: false,
    });
    
    render(<PurchasePage onNavigate={mockOnNavigate} />);

    await user.click(screen.getByTestId('select-package-0'));

    // Should show connect wallet button
    expect(screen.getByText('Connect Wallet to Continue')).toBeInTheDocument();
  });

  it('should handle loading state', () => {
    mockUseOnChainPackages.mockReturnValue({
      packages: [],
      loading: true,
      error: null,
    });
    
    render(<PurchasePage onNavigate={mockOnNavigate} />);

    // Should show skeleton loading
    expect(document.querySelector('.grid')).toBeInTheDocument();
  });

  it('should handle error state', () => {
    mockUseOnChainPackages.mockReturnValue({
      packages: [],
      loading: false,
      error: 'Failed to fetch packages',
    });
    
    render(<PurchasePage onNavigate={mockOnNavigate} />);

    expect(screen.getByText('Error Loading Packages')).toBeInTheDocument();
    expect(screen.getByText('Failed to fetch packages')).toBeInTheDocument();
    expect(screen.getByText('Retry')).toBeInTheDocument();
  });

  it('should handle empty packages state', () => {
    mockUseOnChainPackages.mockReturnValue({
      packages: [],
      loading: false,
      error: null,
    });
    
    render(<PurchasePage onNavigate={mockOnNavigate} />);

    expect(screen.getByText('No Available Packages')).toBeInTheDocument();
    expect(screen.getByText(/currently no investment packages available/)).toBeInTheDocument();
  });
});
