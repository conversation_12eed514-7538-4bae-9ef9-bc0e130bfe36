import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { AdminPage } from '../AdminPage'
import {
  mockUseWriteContract,
  mockUsePackageManager,
  mockGetPackageDetails,
} from '../../test/mocks/wagmi'
import { mockFormattedPackage, mockAdminStats } from '../../test/testData'

// Mock the components
vi.mock('../../components/admin/AddPackageModal', () => ({
  AddPackageModal: ({ isOpen, onClose, onSubmit, loading }: any) => 
    isOpen ? (
      <div data-testid="add-package-modal">
        <h2>Add New Package Modal</h2>
        <button onClick={onClose}>Close</button>
        <button onClick={() => onSubmit({ id: '1', entryAmount: '1000', priceBps: '12', vestSplitBps: '50', referralRateBps: '5' })}>
          Submit
        </button>
        <div data-testid="modal-loading-state">{loading ? 'Loading' : 'Not Loading'}</div>
      </div>
    ) : null
}))

vi.mock('../../components/ui/Card', () => ({
  Card: ({ children, hover }: any) => <div data-testid="card" data-hover={hover}>{children}</div>
}))

vi.mock('../../components/ui/Button', () => ({
  Button: ({ children, onClick, loading, disabled, variant, size, className }: any) => (
    <button 
      onClick={onClick} 
      disabled={disabled || loading}
      data-loading={loading}
      data-variant={variant}
      data-size={size}
      className={className}
    >
      {loading ? 'Loading...' : children}
    </button>
  )
}))

vi.mock('../../components/ui/Input', () => ({
  Input: ({ label, value, onChange, error, helper }: any) => (
    <div>
      <label>{label}</label>
      <input value={value} onChange={onChange} />
      {helper && <span>{helper}</span>}
      {error && <span role="alert">{error}</span>}
    </div>
  )
}))

// Mock framer-motion
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
}))

// Mock lucide-react icons
vi.mock('lucide-react', () => ({
  Users: () => <div data-testid="users-icon" />,
  DollarSign: () => <div data-testid="dollar-sign-icon" />,
  TrendingUp: () => <div data-testid="trending-up-icon" />,
  Settings: () => <div data-testid="settings-icon" />,
  Shield: () => <div data-testid="shield-icon" />,
  BarChart3: () => <div data-testid="bar-chart-icon" />,
  Package: () => <div data-testid="package-icon" />,
  AlertTriangle: () => <div data-testid="alert-triangle-icon" />,
}))

describe('AdminPage Component Integration', () => {
  const mockOnNavigate = vi.fn()
  const user = userEvent.setup()

  beforeEach(() => {
    vi.clearAllMocks()
    
    // Setup default mock returns
    mockUseWriteContract.mockReturnValue({
      writeContractAsync: vi.fn().mockResolvedValue({ hash: '0xmockhash' }),
      isPending: false,
      isSuccess: false,
      error: null,
    })

    mockGetPackageDetails.mockImplementation((id: number) => Promise.resolve({
      entryUSDT: BigInt('1000000000000000000000'),
      priceBps: 1200,
      vestSplitBps: 5000,
      referralRateBps: 500,
      active: true,
      exists: true,
    }))

    mockUsePackageManager.mockReturnValue({
      packageCount: 2,
      getPackageDetails: mockGetPackageDetails,
      isPending: false,
      isSuccess: false,
      error: null,
    })
  })

  describe('Component Rendering', () => {
    it('should render admin page with default overview tab', () => {
      render(<AdminPage onNavigate={mockOnNavigate} />)
      
      expect(screen.getByText('Admin Dashboard')).toBeInTheDocument()
      expect(screen.getByText('System Overview')).toBeInTheDocument()
    })

    it('should render navigation tabs', () => {
      render(<AdminPage onNavigate={mockOnNavigate} />)
      
      expect(screen.getByRole('button', { name: 'Overview' })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: 'Packages' })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: 'Settings' })).toBeInTheDocument()
    })

    it('should render admin stats cards', () => {
      render(<AdminPage onNavigate={mockOnNavigate} />)
      
      expect(screen.getByText('Total Users')).toBeInTheDocument()
      expect(screen.getByText('1,247')).toBeInTheDocument()
      expect(screen.getByText('Total Value Locked')).toBeInTheDocument()
      expect(screen.getByText('$2,547,890')).toBeInTheDocument()
      expect(screen.getByText('Total Transactions')).toBeInTheDocument()
      expect(screen.getByText('12,847')).toBeInTheDocument()
    })
  })

  describe('Tab Navigation', () => {
    it('should switch to packages tab when clicked', async () => {
      render(<AdminPage onNavigate={mockOnNavigate} />)
      
      const packagesTab = screen.getByRole('button', { name: 'Packages' })
      await user.click(packagesTab)
      
      expect(screen.getByText('Investment Packages')).toBeInTheDocument()
    })

    it('should switch to settings tab when clicked', async () => {
      render(<AdminPage onNavigate={mockOnNavigate} />)
      
      const settingsTab = screen.getByRole('button', { name: 'Settings' })
      await user.click(settingsTab)
      
      expect(screen.getByText('System Settings')).toBeInTheDocument()
    })

    it('should switch back to overview tab', async () => {
      render(<AdminPage onNavigate={mockOnNavigate} />)
      
      // Go to packages first
      await user.click(screen.getByRole('button', { name: 'Packages' }))
      expect(screen.getByText('Investment Packages')).toBeInTheDocument()
      
      // Go back to overview
      await user.click(screen.getByRole('button', { name: 'Overview' }))
      expect(screen.getByText('System Overview')).toBeInTheDocument()
    })
  })

  describe('Add New Package Modal Integration', () => {
    it('should open modal when "Add New Package" button is clicked', async () => {
      render(<AdminPage onNavigate={mockOnNavigate} />)
      
      // Switch to packages tab
      await user.click(screen.getByRole('button', { name: 'Packages' }))
      
      const addButton = screen.getByRole('button', { name: 'Add New Package' })
      await user.click(addButton)
      
      expect(screen.getByTestId('add-package-modal')).toBeInTheDocument()
      expect(screen.getByText('Add New Package Modal')).toBeInTheDocument()
    })

    it('should close modal when close button is clicked', async () => {
      render(<AdminPage onNavigate={mockOnNavigate} />)
      
      // Switch to packages tab and open modal
      await user.click(screen.getByRole('button', { name: 'Packages' }))
      await user.click(screen.getByRole('button', { name: 'Add New Package' }))
      
      expect(screen.getByTestId('add-package-modal')).toBeInTheDocument()
      
      // Close modal
      await user.click(screen.getByRole('button', { name: 'Close' }))
      
      expect(screen.queryByTestId('add-package-modal')).not.toBeInTheDocument()
    })

    it('should pass loading state to modal', async () => {
      mockUseWriteContract.mockReturnValue({
        writeContractAsync: vi.fn().mockResolvedValue({ hash: '0xmockhash' }),
        isPending: true,
        isSuccess: false,
        error: null,
      })
      
      render(<AdminPage onNavigate={mockOnNavigate} />)
      
      await user.click(screen.getByRole('button', { name: 'Packages' }))
      await user.click(screen.getByRole('button', { name: 'Add New Package' }))
      
      expect(screen.getByTestId('modal-loading-state')).toHaveTextContent('Loading')
    })
  })

  describe('Package List Display', () => {
    it('should show loading state while fetching packages', () => {
      mockUsePackageManager.mockReturnValue({
        packageCount: 0,
        getPackageDetails: mockGetPackageDetails,
        isPending: true,
        isSuccess: false,
        error: null,
      })
      
      render(<AdminPage onNavigate={mockOnNavigate} />)
      
      // Switch to packages tab
      user.click(screen.getByRole('button', { name: 'Packages' }))
      
      expect(screen.getByText('Loading packages...')).toBeInTheDocument()
    })

    it('should display packages when loaded', async () => {
      render(<AdminPage onNavigate={mockOnNavigate} />)
      
      // Switch to packages tab
      await user.click(screen.getByRole('button', { name: 'Packages' }))
      
      await waitFor(() => {
        expect(screen.getByText('Package 1')).toBeInTheDocument()
        expect(screen.getByText('Package 2')).toBeInTheDocument()
      })
    })

    it('should display package details correctly', async () => {
      render(<AdminPage onNavigate={mockOnNavigate} />)
      
      await user.click(screen.getByRole('button', { name: 'Packages' }))
      
      await waitFor(() => {
        expect(screen.getByText('$1000')).toBeInTheDocument() // Entry amount
        expect(screen.getByText('12%')).toBeInTheDocument() // APY
        expect(screen.getByText('50%')).toBeInTheDocument() // Vesting
        expect(screen.getByText('5%')).toBeInTheDocument() // Referral rate
      })
    })

    it('should show active/inactive status correctly', async () => {
      render(<AdminPage onNavigate={mockOnNavigate} />)
      
      await user.click(screen.getByRole('button', { name: 'Packages' }))
      
      await waitFor(() => {
        expect(screen.getAllByText('Active')).toHaveLength(2) // Both packages are active in mock
      })
    })
  })

  describe('Package Actions', () => {
    it('should render Edit and Enable/Disable buttons for each package', async () => {
      render(<AdminPage onNavigate={mockOnNavigate} />)
      
      await user.click(screen.getByRole('button', { name: 'Packages' }))
      
      await waitFor(() => {
        expect(screen.getAllByRole('button', { name: 'Edit' })).toHaveLength(2)
        expect(screen.getAllByRole('button', { name: 'Disable' })).toHaveLength(2)
      })
    })

    it('should show correct button text based on package status', async () => {
      // Mock one active and one inactive package
      mockGetPackageDetails.mockImplementation((id: number) => Promise.resolve({
        entryUSDT: BigInt('1000000000000000000000'),
        priceBps: 1200,
        vestSplitBps: 5000,
        referralRateBps: 500,
        active: id === 0, // First package active, second inactive
        exists: true,
      }))
      
      render(<AdminPage onNavigate={mockOnNavigate} />)
      
      await user.click(screen.getByRole('button', { name: 'Packages' }))
      
      await waitFor(() => {
        expect(screen.getByRole('button', { name: 'Disable' })).toBeInTheDocument()
        expect(screen.getByRole('button', { name: 'Enable' })).toBeInTheDocument()
      })
    })

    it('should disable action buttons when operation is pending', async () => {
      mockUseWriteContract.mockReturnValue({
        writeContractAsync: vi.fn(),
        isPending: true,
        isSuccess: false,
        error: null,
      })
      
      render(<AdminPage onNavigate={mockOnNavigate} />)
      
      await user.click(screen.getByRole('button', { name: 'Packages' }))
      
      await waitFor(() => {
        const actionButtons = screen.getAllByRole('button', { name: 'Loading...' })
        actionButtons.forEach(button => {
          expect(button).toBeDisabled()
        })
      })
    })
  })

  describe('Error Handling', () => {
    it('should handle package fetching errors gracefully', async () => {
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      mockGetPackageDetails.mockRejectedValue(new Error('Fetch failed'))
      
      render(<AdminPage onNavigate={mockOnNavigate} />)
      
      await user.click(screen.getByRole('button', { name: 'Packages' }))
      
      await waitFor(() => {
        expect(consoleErrorSpy).toHaveBeenCalledWith('Error fetching package 0:', expect.any(Error))
      })
      
      consoleErrorSpy.mockRestore()
    })

    it('should continue loading other packages if one fails', async () => {
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      mockGetPackageDetails.mockImplementation((id: number) => {
        if (id === 0) {
          return Promise.reject(new Error('Fetch failed'))
        }
        return Promise.resolve({
          entryUSDT: BigInt('1000000000000000000000'),
          priceBps: 1200,
          vestSplitBps: 5000,
          referralRateBps: 500,
          active: true,
          exists: true,
        })
      })
      
      render(<AdminPage onNavigate={mockOnNavigate} />)
      
      await user.click(screen.getByRole('button', { name: 'Packages' }))
      
      await waitFor(() => {
        expect(screen.getByText('Package 2')).toBeInTheDocument()
      })
      
      consoleErrorSpy.mockRestore()
    })
  })

  describe('Responsive Design', () => {
    it('should render grid layout for packages', async () => {
      render(<AdminPage onNavigate={mockOnNavigate} />)
      
      await user.click(screen.getByRole('button', { name: 'Packages' }))
      
      await waitFor(() => {
        const packageGrid = screen.getByTestId('card').parentElement
        expect(packageGrid).toHaveClass('grid', 'grid-cols-1', 'md:grid-cols-2', 'lg:grid-cols-3')
      })
    })
  })
})
