import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { AdminPage } from '../AdminPage'
import {
  mockWriteContractAsync,
  mockUseWriteContract,
  mockUsePackageManager,
  mockGetPackageManagerContract,
  mockParseUnits,
} from '../../test/mocks/wagmi'
import { validPackageData, expectedAddPackageArgs, expectedTogglePackageArgs } from '../../test/testData'

// Mock the components
vi.mock('../../components/admin/AddPackageModal', () => ({
  AddPackageModal: ({ isOpen, onClose, onSubmit, loading }: any) => 
    isOpen ? (
      <div data-testid="add-package-modal">
        <button onClick={() => onSubmit(validPackageData)}>Submit Package</button>
        <button onClick={onClose}>Close Modal</button>
        <span data-testid="modal-loading">{loading ? 'loading' : 'not-loading'}</span>
      </div>
    ) : null
}))

vi.mock('../../components/ui/Card', () => ({
  Card: ({ children, hover }: any) => <div data-testid="card" data-hover={hover}>{children}</div>
}))

vi.mock('../../components/ui/Button', () => ({
  Button: ({ children, onClick, loading, disabled, variant, size }: any) => (
    <button 
      onClick={onClick} 
      disabled={disabled || loading}
      data-loading={loading}
      data-variant={variant}
      data-size={size}
    >
      {loading ? 'Loading...' : children}
    </button>
  )
}))

// Mock window.location.reload
Object.defineProperty(window, 'location', {
  value: { reload: vi.fn() },
  writable: true,
})

// Mock alert
global.alert = vi.fn()

describe('AdminPage Smart Contract Integration', () => {
  const mockOnNavigate = vi.fn()
  const user = userEvent.setup()

  beforeEach(() => {
    vi.clearAllMocks()
    
    // Reset window.location.reload mock
    vi.mocked(window.location.reload).mockClear()
    vi.mocked(global.alert).mockClear()
    
    // Setup successful contract responses
    mockWriteContractAsync.mockResolvedValue({ hash: '0xmockhash123' })
    mockUseWriteContract.mockReturnValue({
      writeContractAsync: mockWriteContractAsync,
      isPending: false,
      isSuccess: false,
      error: null,
    })
  })

  describe('addNewPackage Function', () => {
    it('should call writeContractAsync with correct parameters for addPackage', async () => {
      render(<AdminPage onNavigate={mockOnNavigate} />)
      
      // Open modal and submit package
      const addButton = screen.getByRole('button', { name: 'Add New Package' })
      await user.click(addButton)
      
      const submitButton = screen.getByRole('button', { name: 'Submit Package' })
      await user.click(submitButton)
      
      await waitFor(() => {
        expect(mockWriteContractAsync).toHaveBeenCalledWith({
          ...mockGetPackageManagerContract(),
          functionName: 'addPackage',
          args: expectedAddPackageArgs,
        })
      })
    })

    it('should convert parameters correctly for smart contract', async () => {
      render(<AdminPage onNavigate={mockOnNavigate} />)
      
      const addButton = screen.getByRole('button', { name: 'Add New Package' })
      await user.click(addButton)
      
      const submitButton = screen.getByRole('button', { name: 'Submit Package' })
      await user.click(submitButton)
      
      await waitFor(() => {
        const callArgs = mockWriteContractAsync.mock.calls[0][0].args
        
        // Check BigInt conversion for ID
        expect(callArgs[0]).toBe(BigInt(validPackageData.id))
        
        // Check parseUnits call for entry amount
        expect(mockParseUnits).toHaveBeenCalledWith(validPackageData.entryAmount, 18)
        
        // Check basis points conversion (percentage * 100)
        expect(callArgs[2]).toBe(parseInt(validPackageData.priceBps) * 100) // priceBps
        expect(callArgs[3]).toBe(parseInt(validPackageData.vestSplitBps) * 100) // vestSplitBps
        expect(callArgs[4]).toBe(parseInt(validPackageData.referralRateBps) * 100) // referralRateBps
      })
    })

    it('should close modal and reload page on successful submission', async () => {
      render(<AdminPage onNavigate={mockOnNavigate} />)
      
      const addButton = screen.getByRole('button', { name: 'Add New Package' })
      await user.click(addButton)
      
      expect(screen.getByTestId('add-package-modal')).toBeInTheDocument()
      
      const submitButton = screen.getByRole('button', { name: 'Submit Package' })
      await user.click(submitButton)
      
      await waitFor(() => {
        expect(screen.queryByTestId('add-package-modal')).not.toBeInTheDocument()
        expect(window.location.reload).toHaveBeenCalled()
      })
    })

    it('should handle contract call failure', async () => {
      const contractError = new Error('Contract call failed')
      mockWriteContractAsync.mockRejectedValue(contractError)
      
      render(<AdminPage onNavigate={mockOnNavigate} />)
      
      const addButton = screen.getByRole('button', { name: 'Add New Package' })
      await user.click(addButton)
      
      const submitButton = screen.getByRole('button', { name: 'Submit Package' })
      await user.click(submitButton)
      
      await waitFor(() => {
        expect(global.alert).toHaveBeenCalledWith('Failed to add package. Please try again.')
      })
      
      // Modal should remain open on error
      expect(screen.getByTestId('add-package-modal')).toBeInTheDocument()
    })

    it('should show loading state during contract call', async () => {
      // Make the contract call hang
      let resolvePromise: (value: any) => void
      const hangingPromise = new Promise((resolve) => {
        resolvePromise = resolve
      })
      mockWriteContractAsync.mockReturnValue(hangingPromise)
      
      render(<AdminPage onNavigate={mockOnNavigate} />)
      
      const addButton = screen.getByRole('button', { name: 'Add New Package' })
      await user.click(addButton)
      
      const submitButton = screen.getByRole('button', { name: 'Submit Package' })
      await user.click(submitButton)
      
      // Check loading state is shown
      await waitFor(() => {
        expect(screen.getByTestId('modal-loading')).toHaveTextContent('loading')
      })
      
      // Resolve the promise
      resolvePromise!({ hash: '0xmockhash' })
      
      await waitFor(() => {
        expect(screen.getByTestId('modal-loading')).toHaveTextContent('not-loading')
      })
    })
  })

  describe('togglePackageStatus Function', () => {
    beforeEach(() => {
      // Mock packages data
      mockUsePackageManager.mockReturnValue({
        packageCount: 2,
        getPackageDetails: vi.fn().mockImplementation((id: number) => Promise.resolve({
          entryUSDT: BigInt('1000000000000000000000'),
          priceBps: 1200,
          vestSplitBps: 5000,
          referralRateBps: 500,
          active: id === 0, // First package active, second inactive
          exists: true,
        })),
        isPending: false,
        isSuccess: false,
        error: null,
      })
    })

    it('should call disablePackage for active package', async () => {
      render(<AdminPage onNavigate={mockOnNavigate} />)
      
      // Wait for packages to load and switch to packages tab
      await waitFor(() => {
        expect(screen.getByText('Investment Packages')).toBeInTheDocument()
      })
      
      // Find and click disable button for active package
      const disableButtons = screen.getAllByRole('button', { name: 'Disable' })
      await user.click(disableButtons[0])
      
      await waitFor(() => {
        expect(mockWriteContractAsync).toHaveBeenCalledWith({
          ...mockGetPackageManagerContract(),
          functionName: 'disablePackage',
          args: [BigInt(0)],
        })
      })
    })

    it('should call enablePackage for inactive package', async () => {
      render(<AdminPage onNavigate={mockOnNavigate} />)
      
      // Wait for packages to load
      await waitFor(() => {
        expect(screen.getByText('Investment Packages')).toBeInTheDocument()
      })
      
      // Find and click enable button for inactive package
      const enableButtons = screen.getAllByRole('button', { name: 'Enable' })
      await user.click(enableButtons[0])
      
      await waitFor(() => {
        expect(mockWriteContractAsync).toHaveBeenCalledWith({
          ...mockGetPackageManagerContract(),
          functionName: 'enablePackage',
          args: [BigInt(1)],
        })
      })
    })

    it('should reload page on successful toggle', async () => {
      render(<AdminPage onNavigate={mockOnNavigate} />)
      
      await waitFor(() => {
        expect(screen.getByText('Investment Packages')).toBeInTheDocument()
      })
      
      const disableButtons = screen.getAllByRole('button', { name: 'Disable' })
      await user.click(disableButtons[0])
      
      await waitFor(() => {
        expect(window.location.reload).toHaveBeenCalled()
      })
    })

    it('should handle toggle failure with appropriate error message', async () => {
      const contractError = new Error('Toggle failed')
      mockWriteContractAsync.mockRejectedValue(contractError)
      
      render(<AdminPage onNavigate={mockOnNavigate} />)
      
      await waitFor(() => {
        expect(screen.getByText('Investment Packages')).toBeInTheDocument()
      })
      
      const disableButtons = screen.getAllByRole('button', { name: 'Disable' })
      await user.click(disableButtons[0])
      
      await waitFor(() => {
        expect(global.alert).toHaveBeenCalledWith('Failed to disable package. Please try again.')
      })
    })

    it('should show loading state during toggle operation', async () => {
      mockUseWriteContract.mockReturnValue({
        writeContractAsync: mockWriteContractAsync,
        isPending: true,
        isSuccess: false,
        error: null,
      })
      
      render(<AdminPage onNavigate={mockOnNavigate} />)
      
      await waitFor(() => {
        expect(screen.getByText('Investment Packages')).toBeInTheDocument()
      })
      
      // All toggle buttons should be disabled and show loading
      const toggleButtons = screen.getAllByRole('button', { name: 'Loading...' })
      expect(toggleButtons.length).toBeGreaterThan(0)
      
      toggleButtons.forEach(button => {
        expect(button).toBeDisabled()
        expect(button).toHaveAttribute('data-loading', 'true')
      })
    })
  })

  describe('Error Handling', () => {
    it('should handle network errors gracefully', async () => {
      const networkError = new Error('Network error')
      networkError.name = 'NetworkError'
      mockWriteContractAsync.mockRejectedValue(networkError)
      
      render(<AdminPage onNavigate={mockOnNavigate} />)
      
      const addButton = screen.getByRole('button', { name: 'Add New Package' })
      await user.click(addButton)
      
      const submitButton = screen.getByRole('button', { name: 'Submit Package' })
      await user.click(submitButton)
      
      await waitFor(() => {
        expect(global.alert).toHaveBeenCalledWith('Failed to add package. Please try again.')
      })
    })

    it('should handle user rejection gracefully', async () => {
      const userRejection = new Error('User rejected transaction')
      userRejection.name = 'UserRejectedRequestError'
      mockWriteContractAsync.mockRejectedValue(userRejection)
      
      render(<AdminPage onNavigate={mockOnNavigate} />)
      
      const addButton = screen.getByRole('button', { name: 'Add New Package' })
      await user.click(addButton)
      
      const submitButton = screen.getByRole('button', { name: 'Submit Package' })
      await user.click(submitButton)
      
      await waitFor(() => {
        expect(global.alert).toHaveBeenCalledWith('Failed to add package. Please try again.')
      })
    })
  })
})
