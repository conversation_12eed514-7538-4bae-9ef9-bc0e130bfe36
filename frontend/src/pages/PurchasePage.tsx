import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, CheckCircle } from 'lucide-react';
import { OnChainPackage } from '../types';
import { PackageCard } from '../components/purchase/PackageCard';
import { PaymentForm } from '../components/purchase/PaymentForm';
import { Button } from '../components/ui/Button';
import { Card } from '../components/ui/Card';
import { useWallet } from '../hooks/useWallet';
import { WalletModal } from '../components/wallet/WalletModal';
import { usePackageManager } from '../hooks/usePackageManager';
import { useOnChainPackages } from '../hooks/useOnChainPackages';
import { useToast, ToastProvider } from '../components/ui/Toast';
import { PackageGridSkeleton } from '../components/ui/Skeleton';


interface PurchasePageProps {
  onNavigate: (page: string) => void;
}

const PurchasePageContent: React.FC<PurchasePageProps> = ({ onNavigate }) => {
  const [selectedPackage, setSelectedPackage] = useState<OnChainPackage | null>(null);
  const [showPayment, setShowPayment] = useState(false);
  const [paymentComplete, setPaymentComplete] = useState(false);
  const [isWalletModalOpen, setIsWalletModalOpen] = useState(false);
  const [referralCode, setReferralCode] = useState('');

  const { isConnected } = useWallet();
  const { purchasePackage, isPending } = usePackageManager();
  const { packages, loading, error } = useOnChainPackages();
  const toast = useToast();

  // Parse referral code from URL on component mount
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const refParam = urlParams.get('ref');
    if (refParam && refParam.startsWith('0x')) {
      setReferralCode(refParam);
    }
  }, []);

  const handlePackageSelect = (packageId: string) => {
    const pkg = packages.find(p => p.id === packageId);
    setSelectedPackage(pkg || null);
  };

  const handleContinue = () => {
    if (!isConnected) {
      setIsWalletModalOpen(true);
      return;
    }
    setShowPayment(true);
  };

  const handlePayment = async (paymentData: any) => {
    if (!selectedPackage) {
      toast.error('No package selected');
      return;
    }

    try {
      toast.info('Approving USDT...', 'Please confirm the approval transaction');

      // Use the referral code from URL or form input
      const finalReferralCode = paymentData.referralCode || referralCode || '******************************************';

      await purchasePackage(parseInt(selectedPackage.id), finalReferralCode);

      toast.success('Package purchased successfully!', 'Your investment has been processed');
      setPaymentComplete(true);
    } catch (error: any) {
      console.error('Payment failed:', error);

      // Handle specific error types
      if (error.name === 'UserRejectedRequestError') {
        toast.error('Transaction cancelled', 'You cancelled the transaction');
      } else if (error.message?.includes('insufficient funds')) {
        toast.error('Insufficient funds', 'Please ensure you have enough USDT and BNB for gas');
      } else if (error.message?.includes('allowance')) {
        toast.error('Approval failed', 'Please try approving USDT again');
      } else {
        toast.error('Purchase failed', error.message || 'An unexpected error occurred');
      }
    }
  };

  if (paymentComplete) {
    return (
      <div className="min-h-screen bg-dark-50 py-12">
        <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="text-center"
          >
            <Card>
              <div className="w-16 h-16 bg-success-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <CheckCircle className="w-8 h-8 text-success-600" />
              </div>
              <h1 className="text-3xl font-heading font-bold text-dark-900 mb-4">
                Investment Successful!
              </h1>
              <p className="text-dark-600 mb-8">
                Your investment has been processed and tokens will be vested according to your selected package schedule.
              </p>
              <div className="space-y-4">
                <Button onClick={() => onNavigate('dashboard')} className="w-full">
                  View Dashboard
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => onNavigate('home')}
                  className="w-full"
                >
                  Back to Home
                </Button>
              </div>
            </Card>
          </motion.div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-dark-50 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <Button
            variant="ghost"
            onClick={() => showPayment ? setShowPayment(false) : onNavigate('home')}
            className="mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            {showPayment ? 'Back to Packages' : 'Back to Home'}
          </Button>
          
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <h1 className="text-3xl lg:text-4xl font-heading font-bold text-dark-900 mb-4">
              {showPayment ? 'Complete Your Investment' : 'Choose Your Investment Package'}
            </h1>
            <p className="text-xl text-dark-600">
              {showPayment 
                ? 'Review your selection and complete the payment process'
                : 'Select the package that best fits your investment goals and risk tolerance'
              }
            </p>
          </motion.div>
        </div>

        {showPayment ? (
          /* Payment Form */
          <div className="max-w-2xl mx-auto">
            <PaymentForm
              selectedPackage={selectedPackage}
              onPayment={handlePayment}
              referralCode={referralCode}
              isPending={isPending}
            />
          </div>
        ) : (
          /* Package Selection */
          <>
            {loading ? (
              <PackageGridSkeleton count={3} />
            ) : error ? (
              <div className="text-center py-12">
                <Card>
                  <div className="text-center">
                    <h3 className="text-xl font-heading font-semibold text-red-600 mb-4">
                      Error Loading Packages
                    </h3>
                    <p className="text-dark-600 mb-6">{error}</p>
                    <Button onClick={() => window.location.reload()} variant="outline">
                      Retry
                    </Button>
                  </div>
                </Card>
              </div>
            ) : packages.length === 0 ? (
              <div className="text-center py-12">
                <Card>
                  <div className="text-center">
                    <h3 className="text-xl font-heading font-semibold text-dark-900 mb-4">
                      No Available Packages
                    </h3>
                    <p className="text-dark-600">
                      There are currently no investment packages available. Please check back later.
                    </p>
                  </div>
                </Card>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-8">
                {packages.map((pkg, index) => (
                  <motion.div
                    key={pkg.id}
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                  >
                    <PackageCard
                      package={pkg}
                      onSelect={handlePackageSelect}
                      selected={selectedPackage?.id === pkg.id}
                    />
                  </motion.div>
                ))}
              </div>
            )}

            {selectedPackage && !loading && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="max-w-2xl mx-auto"
              >
                <Card>
                  <div className="text-center">
                    <h3 className="text-xl font-heading font-semibold text-dark-900 mb-4">
                      Selected: {selectedPackage.name} Package
                    </h3>
                    <p className="text-dark-600 mb-6">
                      You've selected the {selectedPackage.name} package with {selectedPackage.interestRate}% APY
                      and {selectedPackage.vestingPeriod} months ({Math.floor(selectedPackage.vestingPeriod / 12)} years) vesting period.
                    </p>
                    <Button
                      onClick={handleContinue}
                      size="lg"
                      className="w-full sm:w-auto"
                      disabled={!selectedPackage || isPending}
                      loading={isPending}
                    >
                      {isConnected
                        ? isPending
                          ? 'Processing…'
                          : 'Continue to Payment'
                        : 'Connect Wallet to Continue'}
                    </Button>
                  </div>
                </Card>
              </motion.div>
            )}
          </>
        )}
      </div>

      <WalletModal
        isOpen={isWalletModalOpen}
        onClose={() => setIsWalletModalOpen(false)}
      />
    </div>
  );
};

// Wrap with ToastProvider
export const PurchasePage: React.FC<PurchasePageProps> = (props) => {
  return (
    <ToastProvider>
      <PurchasePageContent {...props} />
    </ToastProvider>
  );
};
