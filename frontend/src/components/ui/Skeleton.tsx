import React from 'react';
import { motion } from 'framer-motion';

interface SkeletonProps {
  className?: string;
  width?: string | number;
  height?: string | number;
  rounded?: boolean;
  animate?: boolean;
}

export const Skeleton: React.FC<SkeletonProps> = ({
  className = '',
  width,
  height,
  rounded = false,
  animate = true
}) => {
  const style = {
    width: typeof width === 'number' ? `${width}px` : width,
    height: typeof height === 'number' ? `${height}px` : height,
  };

  const baseClasses = `bg-gray-200 ${rounded ? 'rounded-full' : 'rounded'} ${className}`;

  if (animate) {
    return (
      <motion.div
        className={baseClasses}
        style={style}
        animate={{
          opacity: [0.5, 1, 0.5],
        }}
        transition={{
          duration: 1.5,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
    );
  }

  return <div className={baseClasses} style={style} />;
};

export const PackageCardSkeleton: React.FC = () => {
  return (
    <div className="bg-white rounded-xl shadow-sm border border-dark-100 p-6">
      <div className="space-y-4">
        {/* Header */}
        <div className="flex items-center justify-between">
          <Skeleton width={120} height={24} />
          <Skeleton width={60} height={20} rounded />
        </div>
        
        {/* Description */}
        <Skeleton width="100%" height={16} />
        <Skeleton width="80%" height={16} />
        
        {/* Amount */}
        <div className="space-y-2">
          <Skeleton width={100} height={14} />
          <Skeleton width="100%" height={32} />
        </div>
        
        {/* Features */}
        <div className="space-y-2">
          <Skeleton width={80} height={14} />
          {[...Array(3)].map((_, i) => (
            <div key={i} className="flex items-center space-x-2">
              <Skeleton width={16} height={16} rounded />
              <Skeleton width={`${Math.random() * 40 + 60}%`} height={14} />
            </div>
          ))}
        </div>
        
        {/* Button */}
        <Skeleton width="100%" height={44} />
      </div>
    </div>
  );
};

export const PackageGridSkeleton: React.FC<{ count?: number }> = ({ count = 3 }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      {[...Array(count)].map((_, i) => (
        <PackageCardSkeleton key={i} />
      ))}
    </div>
  );
};

export const TextSkeleton: React.FC<{ lines?: number; className?: string }> = ({ 
  lines = 1, 
  className = '' 
}) => {
  return (
    <div className={`space-y-2 ${className}`}>
      {[...Array(lines)].map((_, i) => (
        <Skeleton 
          key={i} 
          width={i === lines - 1 ? `${Math.random() * 30 + 70}%` : '100%'} 
          height={16} 
        />
      ))}
    </div>
  );
};
