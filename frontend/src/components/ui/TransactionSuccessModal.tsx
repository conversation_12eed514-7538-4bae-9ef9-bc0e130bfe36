import React from 'react';
import { motion } from 'framer-motion';
import { CheckCircle, ExternalLink, Copy } from 'lucide-react';
import { Modal } from './Modal';
import { Button } from './Button';
import { useBlockExplorer } from '../../utils/transaction';
import { useToast } from './Toast';

interface TransactionSuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
  transactionHash: string;
  title?: string;
  description?: string;
  amount?: string;
  packageName?: string;
}

export const TransactionSuccessModal: React.FC<TransactionSuccessModalProps> = ({
  isOpen,
  onClose,
  transactionHash,
  title = 'Transaction Successful!',
  description = 'Your transaction has been confirmed on the blockchain.',
  amount,
  packageName
}) => {
  const { getTransactionDetails, getTransactionUrl, explorerInfo } = useBlockExplorer();
  const toast = useToast();

  const transactionDetails = getTransactionDetails(transactionHash);
  const fullTransactionUrl = getTransactionUrl(transactionHash);

  const handleCopyHash = async () => {
    try {
      await navigator.clipboard.writeText(transactionHash);
      toast.success('Copied!', 'Transaction hash copied to clipboard');
    } catch (error) {
      // Fallback for browsers that don't support clipboard API
      const textArea = document.createElement('textarea');
      textArea.value = transactionHash;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      toast.success('Copied!', 'Transaction hash copied to clipboard');
    }
  };

  const handleViewOnExplorer = () => {
    window.open(fullTransactionUrl, '_blank', 'noopener,noreferrer');
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title=""
      size="md"
    >
      <div className="text-center">
        {/* Success Animation */}
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ 
            type: "spring",
            stiffness: 260,
            damping: 20,
            delay: 0.1
          }}
          className="mx-auto flex items-center justify-center w-16 h-16 bg-success-100 rounded-full mb-6"
        >
          <CheckCircle className="w-8 h-8 text-success-600" />
        </motion.div>

        {/* Title and Description */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <h3 className="text-xl font-heading font-semibold text-dark-900 mb-2">
            {title}
          </h3>
          <p className="text-dark-600 mb-6">
            {description}
          </p>
        </motion.div>

        {/* Transaction Details */}
        {(amount || packageName) && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-dark-50 rounded-lg p-4 mb-6"
          >
            {packageName && (
              <div className="flex justify-between items-center mb-2">
                <span className="text-dark-600">Package:</span>
                <span className="font-medium text-dark-900">{packageName}</span>
              </div>
            )}
            {amount && (
              <div className="flex justify-between items-center">
                <span className="text-dark-600">Amount:</span>
                <span className="font-medium text-dark-900">${amount}</span>
              </div>
            )}
          </motion.div>
        )}

        {/* Transaction Hash */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-dark-50 rounded-lg p-4 mb-6"
        >
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-dark-700">Transaction Hash</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleCopyHash}
              className="p-1 h-auto"
              aria-label="Copy transaction hash"
            >
              <Copy className="w-4 h-4" />
            </Button>
          </div>
          <div className="font-mono text-sm text-dark-600 break-all">
            {transactionDetails.hash}
          </div>
        </motion.div>

        {/* Action Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="flex flex-col sm:flex-row gap-3"
        >
          <Button
            onClick={handleViewOnExplorer}
            className="flex items-center justify-center gap-2"
            variant="outline"
          >
            <ExternalLink className="w-4 h-4" />
            View on {explorerInfo.name}
          </Button>
          <Button
            onClick={onClose}
            className="flex-1"
          >
            Continue
          </Button>
        </motion.div>

        {/* Additional Info */}
        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.6 }}
          className="text-xs text-dark-500 mt-4"
        >
          Your transaction is now being processed on the blockchain. 
          It may take a few minutes to be fully confirmed.
        </motion.p>
      </div>
    </Modal>
  );
};
