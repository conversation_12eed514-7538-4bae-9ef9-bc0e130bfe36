import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { TransactionSuccessModal } from '../TransactionSuccessModal';

// Mock the transaction utilities
vi.mock('../../../utils/transaction', () => ({
  useBlockExplorer: vi.fn(() => ({
    getTransactionDetails: vi.fn((hash: string) => ({
      hash: '0x1234...cdef',
      explorerUrl: `https://testnet.bscscan.com/tx/${hash}`,
      explorerName: 'BSCScan Testnet'
    })),
    getTransactionUrl: vi.fn((hash: string) => `https://testnet.bscscan.com/tx/${hash}`),
    explorerInfo: {
      url: 'https://testnet.bscscan.com',
      name: 'BSCScan Testnet'
    }
  }))
}));

// Mock the Toast hook
const mockToast = {
  success: vi.fn(),
  error: vi.fn(),
  info: vi.fn(),
  warning: vi.fn(),
};

vi.mock('../Toast', () => ({
  useToast: () => mockToast,
}));

// Mock window.open
const mockWindowOpen = vi.fn();
Object.defineProperty(window, 'open', {
  value: mockWindowOpen,
  writable: true,
});

describe('TransactionSuccessModal', () => {
  const defaultProps = {
    isOpen: true,
    onClose: vi.fn(),
    transactionHash: '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef',
  };

  // Mock clipboard API
  const mockClipboard = {
    writeText: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockClipboard.writeText.mockResolvedValue(undefined);

    // Mock clipboard API
    Object.defineProperty(navigator, 'clipboard', {
      value: mockClipboard,
      writable: true,
      configurable: true,
    });
  });

  it('should render the modal when open', () => {
    render(<TransactionSuccessModal {...defaultProps} />);
    
    expect(screen.getByText('Transaction Successful!')).toBeInTheDocument();
    expect(screen.getByText('Your transaction has been confirmed on the blockchain.')).toBeInTheDocument();
    expect(screen.getByText('Transaction Hash')).toBeInTheDocument();
    expect(screen.getByText('0x1234...cdef')).toBeInTheDocument();
  });

  it('should not render when closed', () => {
    render(<TransactionSuccessModal {...defaultProps} isOpen={false} />);
    
    expect(screen.queryByText('Transaction Successful!')).not.toBeInTheDocument();
  });

  it('should display custom title and description', () => {
    render(
      <TransactionSuccessModal 
        {...defaultProps} 
        title="Custom Title"
        description="Custom description"
      />
    );
    
    expect(screen.getByText('Custom Title')).toBeInTheDocument();
    expect(screen.getByText('Custom description')).toBeInTheDocument();
  });

  it('should display package details when provided', () => {
    render(
      <TransactionSuccessModal 
        {...defaultProps} 
        amount="1000"
        packageName="Premium Package"
      />
    );
    
    expect(screen.getByText('Package:')).toBeInTheDocument();
    expect(screen.getByText('Premium Package')).toBeInTheDocument();
    expect(screen.getByText('Amount:')).toBeInTheDocument();
    expect(screen.getByText('$1000')).toBeInTheDocument();
  });

  it('should copy transaction hash to clipboard when copy button is clicked', async () => {
    const user = userEvent.setup();
    render(<TransactionSuccessModal {...defaultProps} />);

    const copyButton = screen.getByRole('button', { name: 'Copy transaction hash' });
    await user.click(copyButton);

    expect(mockClipboard.writeText).toHaveBeenCalledWith(defaultProps.transactionHash);
    expect(mockToast.success).toHaveBeenCalledWith('Copied!', 'Transaction hash copied to clipboard');
  });

  it('should handle clipboard API failure gracefully', async () => {
    const user = userEvent.setup();
    mockClipboard.writeText.mockRejectedValue(new Error('Clipboard not available'));

    // Mock document.execCommand for fallback
    const mockExecCommand = vi.fn().mockReturnValue(true);
    document.execCommand = mockExecCommand;

    render(<TransactionSuccessModal {...defaultProps} />);

    const copyButton = screen.getByRole('button', { name: 'Copy transaction hash' });
    await user.click(copyButton);

    await waitFor(() => {
      expect(mockToast.success).toHaveBeenCalledWith('Copied!', 'Transaction hash copied to clipboard');
    });
  });

  it('should open block explorer when "View on Explorer" button is clicked', async () => {
    const user = userEvent.setup();
    render(<TransactionSuccessModal {...defaultProps} />);
    
    const explorerButton = screen.getByText('View on BSCScan Testnet');
    await user.click(explorerButton);
    
    expect(mockWindowOpen).toHaveBeenCalledWith(
      `https://testnet.bscscan.com/tx/${defaultProps.transactionHash}`,
      '_blank',
      'noopener,noreferrer'
    );
  });

  it('should call onClose when Continue button is clicked', async () => {
    const user = userEvent.setup();
    const mockOnClose = vi.fn();
    render(<TransactionSuccessModal {...defaultProps} onClose={mockOnClose} />);
    
    const continueButton = screen.getByText('Continue');
    await user.click(continueButton);
    
    expect(mockOnClose).toHaveBeenCalled();
  });

  it('should display additional info text', () => {
    render(<TransactionSuccessModal {...defaultProps} />);
    
    expect(screen.getByText(/Your transaction is now being processed on the blockchain/)).toBeInTheDocument();
  });
});
