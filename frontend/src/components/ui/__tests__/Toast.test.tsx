import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { ToastProvider, useToast } from '../Toast';

// Test component that uses the toast hook
const TestComponent = () => {
  const toast = useToast();

  return (
    <div>
      <button onClick={() => toast.success('Success!', 'Operation completed')}>
        Success Toast
      </button>
      <button onClick={() => toast.error('Error!', 'Something went wrong')}>
        Error Toast
      </button>
      <button onClick={() => toast.warning('Warning!', 'Be careful')}>
        Warning Toast
      </button>
      <button onClick={() => toast.info('Info!', 'Just so you know')}>
        Info Toast
      </button>
    </div>
  );
};

describe('Toast', () => {
  beforeEach(() => {
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.runOnlyPendingTimers();
    vi.useRealTimers();
  });

  it('should throw error when useToast is used outside ToastProvider', () => {
    // Suppress console.error for this test
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    
    expect(() => {
      render(<TestComponent />);
    }).toThrow('useToast must be used within a ToastProvider');
    
    consoleSpy.mockRestore();
  });

  it('should display success toast', async () => {
    const user = userEvent.setup({ advanceTimers: vi.advanceTimersByTime });
    
    render(
      <ToastProvider>
        <TestComponent />
      </ToastProvider>
    );

    await user.click(screen.getByText('Success Toast'));

    expect(screen.getByText('Success!')).toBeInTheDocument();
    expect(screen.getByText('Operation completed')).toBeInTheDocument();
  });

  it('should display error toast', async () => {
    const user = userEvent.setup({ advanceTimers: vi.advanceTimersByTime });
    
    render(
      <ToastProvider>
        <TestComponent />
      </ToastProvider>
    );

    await user.click(screen.getByText('Error Toast'));

    expect(screen.getByText('Error!')).toBeInTheDocument();
    expect(screen.getByText('Something went wrong')).toBeInTheDocument();
  });

  it('should display warning toast', async () => {
    const user = userEvent.setup({ advanceTimers: vi.advanceTimersByTime });
    
    render(
      <ToastProvider>
        <TestComponent />
      </ToastProvider>
    );

    await user.click(screen.getByText('Warning Toast'));

    expect(screen.getByText('Warning!')).toBeInTheDocument();
    expect(screen.getByText('Be careful')).toBeInTheDocument();
  });

  it('should display info toast', async () => {
    const user = userEvent.setup({ advanceTimers: vi.advanceTimersByTime });
    
    render(
      <ToastProvider>
        <TestComponent />
      </ToastProvider>
    );

    await user.click(screen.getByText('Info Toast'));

    expect(screen.getByText('Info!')).toBeInTheDocument();
    expect(screen.getByText('Just so you know')).toBeInTheDocument();
  });

  it('should auto-remove toast after duration', async () => {
    const user = userEvent.setup({ advanceTimers: vi.advanceTimersByTime });
    
    render(
      <ToastProvider>
        <TestComponent />
      </ToastProvider>
    );

    await user.click(screen.getByText('Success Toast'));

    expect(screen.getByText('Success!')).toBeInTheDocument();

    // Fast-forward time by 5 seconds (default duration)
    vi.advanceTimersByTime(5000);

    await waitFor(() => {
      expect(screen.queryByText('Success!')).not.toBeInTheDocument();
    });
  });

  it('should remove toast when close button is clicked', async () => {
    const user = userEvent.setup({ advanceTimers: vi.advanceTimersByTime });
    
    render(
      <ToastProvider>
        <TestComponent />
      </ToastProvider>
    );

    await user.click(screen.getByText('Success Toast'));

    expect(screen.getByText('Success!')).toBeInTheDocument();

    // Click the close button (X icon)
    const closeButton = screen.getByRole('button', { name: '' }); // X button has no text
    await user.click(closeButton);

    await waitFor(() => {
      expect(screen.queryByText('Success!')).not.toBeInTheDocument();
    });
  });

  it('should display multiple toasts', async () => {
    const user = userEvent.setup({ advanceTimers: vi.advanceTimersByTime });
    
    render(
      <ToastProvider>
        <TestComponent />
      </ToastProvider>
    );

    await user.click(screen.getByText('Success Toast'));
    await user.click(screen.getByText('Error Toast'));

    expect(screen.getByText('Success!')).toBeInTheDocument();
    expect(screen.getByText('Error!')).toBeInTheDocument();
  });

  it('should use longer duration for error toasts', async () => {
    const user = userEvent.setup({ advanceTimers: vi.advanceTimersByTime });
    
    render(
      <ToastProvider>
        <TestComponent />
      </ToastProvider>
    );

    await user.click(screen.getByText('Error Toast'));

    expect(screen.getByText('Error!')).toBeInTheDocument();

    // Fast-forward by 5 seconds (normal duration)
    vi.advanceTimersByTime(5000);

    // Error toast should still be visible (7 second duration)
    expect(screen.getByText('Error!')).toBeInTheDocument();

    // Fast-forward by 2 more seconds
    vi.advanceTimersByTime(2000);

    await waitFor(() => {
      expect(screen.queryByText('Error!')).not.toBeInTheDocument();
    });
  });

  it('should display toast without message', async () => {
    const TestComponentNoMessage = () => {
      const toast = useToast();
      return (
        <button onClick={() => toast.success('Title only')}>
          Title Only Toast
        </button>
      );
    };

    const user = userEvent.setup({ advanceTimers: vi.advanceTimersByTime });
    
    render(
      <ToastProvider>
        <TestComponentNoMessage />
      </ToastProvider>
    );

    await user.click(screen.getByText('Title Only Toast'));

    expect(screen.getByText('Title only')).toBeInTheDocument();
    // Should not have a message element
    expect(screen.queryByText('Operation completed')).not.toBeInTheDocument();
  });

  it('should handle rapid toast creation', async () => {
    const user = userEvent.setup({ advanceTimers: vi.advanceTimersByTime });
    
    render(
      <ToastProvider>
        <TestComponent />
      </ToastProvider>
    );

    // Create multiple toasts rapidly
    await user.click(screen.getByText('Success Toast'));
    await user.click(screen.getByText('Error Toast'));
    await user.click(screen.getByText('Warning Toast'));
    await user.click(screen.getByText('Info Toast'));

    // All should be visible
    expect(screen.getByText('Success!')).toBeInTheDocument();
    expect(screen.getByText('Error!')).toBeInTheDocument();
    expect(screen.getByText('Warning!')).toBeInTheDocument();
    expect(screen.getByText('Info!')).toBeInTheDocument();
  });
});
