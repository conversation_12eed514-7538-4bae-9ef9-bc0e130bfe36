import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { AddPackageModal } from '../AddPackageModal'
import { validPackageData, invalidPackageData } from '../../../test/testData'

// Mock the UI components
vi.mock('../../ui/Modal', () => ({
  Modal: ({ children, isOpen, title }: any) => 
    isOpen ? <div data-testid="modal" aria-label={title}>{children}</div> : null
}))

vi.mock('../../ui/Button', () => ({
  Button: ({ children, onClick, loading, disabled, type, ...props }: any) => (
    <button 
      onClick={onClick} 
      disabled={disabled || loading}
      type={type}
      data-loading={loading}
      {...props}
    >
      {loading ? 'Loading...' : children}
    </button>
  )
}))

vi.mock('../../ui/Input', () => ({
  Input: ({ label, value, onChange, error, helper, disabled, required, type, step }: any) => (
    <div>
      <label>{label}{required && ' *'}</label>
      <input
        type={type}
        step={step}
        value={value}
        onChange={onChange}
        disabled={disabled}
        data-testid={`input-${label.toLowerCase().replace(/[^a-z0-9]/g, '-')}`}
      />
      {helper && <span data-testid={`helper-${label.toLowerCase().replace(/[^a-z0-9]/g, '-')}`}>{helper}</span>}
      {error && <span data-testid={`error-${label.toLowerCase().replace(/[^a-z0-9]/g, '-')}`} role="alert">{error}</span>}
    </div>
  )
}))

describe('AddPackageModal', () => {
  const mockOnClose = vi.fn()
  const mockOnSubmit = vi.fn()
  const user = userEvent.setup()

  const defaultProps = {
    isOpen: true,
    onClose: mockOnClose,
    onSubmit: mockOnSubmit,
    loading: false,
  }

  beforeEach(() => {
    vi.clearAllMocks()
    mockOnSubmit.mockResolvedValue(undefined)
  })

  describe('Rendering', () => {
    it('should render modal when isOpen is true', () => {
      render(<AddPackageModal {...defaultProps} />)
      expect(screen.getByTestId('modal')).toBeInTheDocument()
      expect(screen.getByLabelText('Add New Package')).toBeInTheDocument()
    })

    it('should not render modal when isOpen is false', () => {
      render(<AddPackageModal {...defaultProps} isOpen={false} />)
      expect(screen.queryByTestId('modal')).not.toBeInTheDocument()
    })

    it('should render all form fields with correct labels and helpers', () => {
      render(<AddPackageModal {...defaultProps} />)
      
      expect(screen.getByTestId('input-package-id')).toBeInTheDocument()
      expect(screen.getByTestId('helper-package-id')).toHaveTextContent('Unique identifier for the package')

      expect(screen.getByTestId('input-entry-amount--usdt-')).toBeInTheDocument()
      expect(screen.getByTestId('helper-entry-amount--usdt-')).toHaveTextContent('Minimum investment amount in USDT')

      expect(screen.getByTestId('input-apy----')).toBeInTheDocument()
      expect(screen.getByTestId('helper-apy----')).toHaveTextContent('Annual percentage yield')

      expect(screen.getByTestId('input-vesting-split----')).toBeInTheDocument()
      expect(screen.getByTestId('helper-vesting-split----')).toHaveTextContent('Percentage of tokens to vest')

      expect(screen.getByTestId('input-referral-rate----')).toBeInTheDocument()
      expect(screen.getByTestId('helper-referral-rate----')).toHaveTextContent('Referral bonus percentage')
    })

    it('should render package preview section', () => {
      render(<AddPackageModal {...defaultProps} />)
      expect(screen.getByText('Package Preview')).toBeInTheDocument()
      expect(screen.getByText('Entry Amount: $0 USDT')).toBeInTheDocument()
      expect(screen.getByText('APY: 0%')).toBeInTheDocument()
      expect(screen.getByText('Vesting: 0% of tokens')).toBeInTheDocument()
      expect(screen.getByText('Referral Bonus: 0%')).toBeInTheDocument()
    })

    it('should render form buttons', () => {
      render(<AddPackageModal {...defaultProps} />)
      expect(screen.getByRole('button', { name: 'Cancel' })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: 'Add Package' })).toBeInTheDocument()
    })
  })

  describe('Form Validation', () => {
    it('should show error for empty package ID', async () => {
      render(<AddPackageModal {...defaultProps} />)
      
      const submitButton = screen.getByRole('button', { name: 'Add Package' })
      await user.click(submitButton)
      
      expect(screen.getByTestId('error-package-id')).toHaveTextContent('Package ID is required')
    })

    it('should show error for zero package ID', async () => {
      render(<AddPackageModal {...defaultProps} />)
      
      const idInput = screen.getByTestId('input-package-id')
      await user.type(idInput, '0')
      
      const submitButton = screen.getByRole('button', { name: 'Add Package' })
      await user.click(submitButton)
      
      expect(screen.getByTestId('error-package-id')).toHaveTextContent('Package ID must be greater than 0')
    })

    it('should show error for empty entry amount', async () => {
      render(<AddPackageModal {...defaultProps} />)
      
      const idInput = screen.getByTestId('input-package-id')
      await user.type(idInput, '1')
      
      const submitButton = screen.getByRole('button', { name: 'Add Package' })
      await user.click(submitButton)
      
      expect(screen.getByTestId('error-entry-amount--usdt-')).toHaveTextContent('Entry amount is required')
    })

    it('should show error for zero entry amount', async () => {
      render(<AddPackageModal {...defaultProps} />)
      
      const idInput = screen.getByTestId('input-package-id')
      const entryInput = screen.getByTestId('input-entry-amount--usdt-')
      
      await user.type(idInput, '1')
      await user.type(entryInput, '0')
      
      const submitButton = screen.getByRole('button', { name: 'Add Package' })
      await user.click(submitButton)
      
      expect(screen.getByTestId('error-entry-amount--usdt-')).toHaveTextContent('Entry amount must be greater than 0')
    })

    it('should show error for invalid price range', async () => {
      render(<AddPackageModal {...defaultProps} />)
      
      const idInput = screen.getByTestId('input-package-id')
      const entryInput = screen.getByTestId('input-entry-amount--usdt-')
      const priceInput = screen.getByTestId('input-apy----')

      await user.type(idInput, '1')
      await user.type(entryInput, '1000')
      await user.type(priceInput, '101')

      const submitButton = screen.getByRole('button', { name: 'Add Package' })
      await user.click(submitButton)

      expect(screen.getByTestId('error-apy----')).toHaveTextContent('Price must be between 0 and 100%')
    })

    it('should show error for zero price', async () => {
      render(<AddPackageModal {...defaultProps} />)
      
      const idInput = screen.getByTestId('input-package-id')
      const entryInput = screen.getByTestId('input-entry-amount--usdt-')
      const priceInput = screen.getByTestId('input-apy----')

      await user.type(idInput, '1')
      await user.type(entryInput, '1000')
      await user.type(priceInput, '0')

      const submitButton = screen.getByRole('button', { name: 'Add Package' })
      await user.click(submitButton)

      expect(screen.getByTestId('error-apy----')).toHaveTextContent('Price must be between 0 and 100%')
    })

    it('should show error for invalid vesting split range', async () => {
      render(<AddPackageModal {...defaultProps} />)
      
      const idInput = screen.getByTestId('input-package-id')
      const entryInput = screen.getByTestId('input-entry-amount--usdt-')
      const priceInput = screen.getByTestId('input-price--apy--')
      const vestingInput = screen.getByTestId('input-vesting-split--')
      
      await user.type(idInput, '1')
      await user.type(entryInput, '1000')
      await user.type(priceInput, '12')
      await user.type(vestingInput, '101')
      
      const submitButton = screen.getByRole('button', { name: 'Add Package' })
      await user.click(submitButton)
      
      expect(screen.getByTestId('error-vesting-split--')).toHaveTextContent('Vesting split must be between 0 and 100%')
    })

    it('should show error for invalid referral rate range', async () => {
      render(<AddPackageModal {...defaultProps} />)
      
      const idInput = screen.getByTestId('input-package-id')
      const entryInput = screen.getByTestId('input-entry-amount--usdt-')
      const priceInput = screen.getByTestId('input-price--apy--')
      const vestingInput = screen.getByTestId('input-vesting-split--')
      const referralInput = screen.getByTestId('input-referral-rate--')
      
      await user.type(idInput, '1')
      await user.type(entryInput, '1000')
      await user.type(priceInput, '12')
      await user.type(vestingInput, '50')
      await user.type(referralInput, '101')
      
      const submitButton = screen.getByRole('button', { name: 'Add Package' })
      await user.click(submitButton)
      
      expect(screen.getByTestId('error-referral-rate--')).toHaveTextContent('Referral rate must be between 0 and 100%')
    })

    it('should clear errors when user starts typing', async () => {
      render(<AddPackageModal {...defaultProps} />)
      
      const submitButton = screen.getByRole('button', { name: 'Add Package' })
      await user.click(submitButton)
      
      expect(screen.getByTestId('error-package-id')).toBeInTheDocument()
      
      const idInput = screen.getByTestId('input-package-id')
      await user.type(idInput, '1')
      
      expect(screen.queryByTestId('error-package-id')).not.toBeInTheDocument()
    })
  })

  describe('Form Submission', () => {
    it('should submit form with valid data', async () => {
      render(<AddPackageModal {...defaultProps} />)
      
      // Fill in valid form data
      await user.type(screen.getByTestId('input-package-id'), validPackageData.id)
      await user.type(screen.getByTestId('input-entry-amount--usdt-'), validPackageData.entryAmount)
      await user.type(screen.getByTestId('input-price--apy--'), validPackageData.priceBps)
      await user.type(screen.getByTestId('input-vesting-split--'), validPackageData.vestSplitBps)
      await user.type(screen.getByTestId('input-referral-rate--'), validPackageData.referralRateBps)
      
      const submitButton = screen.getByRole('button', { name: 'Add Package' })
      await user.click(submitButton)
      
      await waitFor(() => {
        expect(mockOnSubmit).toHaveBeenCalledWith(validPackageData)
      })
    })

    it('should reset form after successful submission', async () => {
      render(<AddPackageModal {...defaultProps} />)
      
      // Fill in form data
      await user.type(screen.getByTestId('input-package-id'), validPackageData.id)
      await user.type(screen.getByTestId('input-entry-amount--usdt-'), validPackageData.entryAmount)
      await user.type(screen.getByTestId('input-price--apy--'), validPackageData.priceBps)
      await user.type(screen.getByTestId('input-vesting-split--'), validPackageData.vestSplitBps)
      await user.type(screen.getByTestId('input-referral-rate--'), validPackageData.referralRateBps)
      
      const submitButton = screen.getByRole('button', { name: 'Add Package' })
      await user.click(submitButton)
      
      await waitFor(() => {
        expect(screen.getByTestId('input-package-id')).toHaveValue('')
        expect(screen.getByTestId('input-entry-amount--usdt-')).toHaveValue('')
        expect(screen.getByTestId('input-price--apy--')).toHaveValue('')
        expect(screen.getByTestId('input-vesting-split--')).toHaveValue('')
        expect(screen.getByTestId('input-referral-rate--')).toHaveValue('')
      })
    })

    it('should not submit form with invalid data', async () => {
      render(<AddPackageModal {...defaultProps} />)
      
      const submitButton = screen.getByRole('button', { name: 'Add Package' })
      await user.click(submitButton)
      
      expect(mockOnSubmit).not.toHaveBeenCalled()
    })

    it('should handle submission errors gracefully', async () => {
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      mockOnSubmit.mockRejectedValue(new Error('Submission failed'))
      
      render(<AddPackageModal {...defaultProps} />)
      
      // Fill in valid form data
      await user.type(screen.getByTestId('input-package-id'), validPackageData.id)
      await user.type(screen.getByTestId('input-entry-amount--usdt-'), validPackageData.entryAmount)
      await user.type(screen.getByTestId('input-price--apy--'), validPackageData.priceBps)
      await user.type(screen.getByTestId('input-vesting-split--'), validPackageData.vestSplitBps)
      await user.type(screen.getByTestId('input-referral-rate--'), validPackageData.referralRateBps)
      
      const submitButton = screen.getByRole('button', { name: 'Add Package' })
      await user.click(submitButton)
      
      await waitFor(() => {
        expect(consoleErrorSpy).toHaveBeenCalledWith('Error submitting package:', expect.any(Error))
      })
      
      consoleErrorSpy.mockRestore()
    })
  })

  describe('Loading States', () => {
    it('should disable form inputs when loading', () => {
      render(<AddPackageModal {...defaultProps} loading={true} />)

      expect(screen.getByTestId('input-package-id')).toBeDisabled()
      expect(screen.getByTestId('input-entry-amount--usdt-')).toBeDisabled()
      expect(screen.getByTestId('input-price--apy--')).toBeDisabled()
      expect(screen.getByTestId('input-vesting-split--')).toBeDisabled()
      expect(screen.getByTestId('input-referral-rate--')).toBeDisabled()
    })

    it('should disable submit button when loading', () => {
      render(<AddPackageModal {...defaultProps} loading={true} />)

      const submitButton = screen.getByRole('button', { name: 'Loading...' })
      expect(submitButton).toBeDisabled()
      expect(submitButton).toHaveAttribute('data-loading', 'true')
    })

    it('should prevent modal close when loading', async () => {
      render(<AddPackageModal {...defaultProps} loading={true} />)

      const cancelButton = screen.getByRole('button', { name: 'Cancel' })
      await user.click(cancelButton)

      expect(mockOnClose).not.toHaveBeenCalled()
    })
  })

  describe('Modal Functionality', () => {
    it('should call onClose when cancel button is clicked', async () => {
      render(<AddPackageModal {...defaultProps} />)

      const cancelButton = screen.getByRole('button', { name: 'Cancel' })
      await user.click(cancelButton)

      expect(mockOnClose).toHaveBeenCalled()
    })

    it('should reset form when modal is closed', async () => {
      render(<AddPackageModal {...defaultProps} />)

      // Fill in some data
      await user.type(screen.getByTestId('input-package-id'), '1')
      await user.type(screen.getByTestId('input-entry-amount--usdt-'), '1000')

      const cancelButton = screen.getByRole('button', { name: 'Cancel' })
      await user.click(cancelButton)

      expect(mockOnClose).toHaveBeenCalled()

      // Re-render to simulate modal reopening
      render(<AddPackageModal {...defaultProps} />)

      expect(screen.getByTestId('input-package-id')).toHaveValue('')
      expect(screen.getByTestId('input-entry-amount--usdt-')).toHaveValue('')
    })
  })

  describe('Package Preview', () => {
    it('should update preview when form values change', async () => {
      render(<AddPackageModal {...defaultProps} />)

      await user.type(screen.getByTestId('input-entry-amount--usdt-'), '1000')
      await user.type(screen.getByTestId('input-price--apy--'), '12')
      await user.type(screen.getByTestId('input-vesting-split--'), '50')
      await user.type(screen.getByTestId('input-referral-rate--'), '5')

      expect(screen.getByText('Entry Amount: $1000 USDT')).toBeInTheDocument()
      expect(screen.getByText('APY: 12%')).toBeInTheDocument()
      expect(screen.getByText('Vesting: 50% of tokens')).toBeInTheDocument()
      expect(screen.getByText('Referral Bonus: 5%')).toBeInTheDocument()
    })

    it('should show default values in preview when form is empty', () => {
      render(<AddPackageModal {...defaultProps} />)

      expect(screen.getByText('Entry Amount: $0 USDT')).toBeInTheDocument()
      expect(screen.getByText('APY: 0%')).toBeInTheDocument()
      expect(screen.getByText('Vesting: 0% of tokens')).toBeInTheDocument()
      expect(screen.getByText('Referral Bonus: 0%')).toBeInTheDocument()
    })
  })

  describe('Input Types and Attributes', () => {
    it('should have correct input types and attributes', () => {
      render(<AddPackageModal {...defaultProps} />)

      const idInput = screen.getByTestId('input-package-id')
      expect(idInput).toHaveAttribute('type', 'number')

      const entryInput = screen.getByTestId('input-entry-amount--usdt-')
      expect(entryInput).toHaveAttribute('type', 'number')
      expect(entryInput).toHaveAttribute('step', '0.01')

      const priceInput = screen.getByTestId('input-price--apy--')
      expect(priceInput).toHaveAttribute('type', 'number')
      expect(priceInput).toHaveAttribute('step', '0.01')

      const vestingInput = screen.getByTestId('input-vesting-split--')
      expect(vestingInput).toHaveAttribute('type', 'number')
      expect(vestingInput).toHaveAttribute('step', '0.01')

      const referralInput = screen.getByTestId('input-referral-rate--')
      expect(referralInput).toHaveAttribute('type', 'number')
      expect(referralInput).toHaveAttribute('step', '0.01')
    })
  })
})
