{"name": "blockcoop-defi-platform", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest --config vite.config.test.ts", "test:ui": "vitest --ui --config vite.config.test.ts", "test:run": "vitest run --config vite.config.test.ts", "test:watch": "vitest --watch --config vite.config.test.ts", "test:coverage": "vitest run --coverage --config vite.config.test.ts", "test:unit": "vitest run --config vite.config.test.ts \"src/**/*.test.{ts,tsx}\"", "test:integration": "vitest run --config vite.config.test.ts \"src/**/*.integration.test.{ts,tsx}\"", "test:admin": "vitest run --config vite.config.test.ts \"src/{components/admin,pages}/**/*.{test,spec}.{ts,tsx}\"", "test:admin-watch": "vitest --watch --config vite.config.test.ts \"src/{components/admin,pages}/**/*.{test,spec}.{ts,tsx}\"", "test:admin-coverage": "vitest run --coverage --config vite.config.test.ts \"src/{components/admin,pages}/**/*.{test,spec}.{ts,tsx}\""}, "dependencies": {"@reown/appkit": "^1.0.0", "@reown/appkit-adapter-wagmi": "^1.0.0", "@tanstack/react-query": "^5.59.0", "framer-motion": "^11.11.1", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.28.0", "recharts": "^2.12.7", "viem": "^2.21.19", "wagmi": "^2.12.17"}, "devDependencies": {"@eslint/js": "^9.9.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.6.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "@vitest/coverage-v8": "^3.2.2", "@vitest/ui": "^3.2.2", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "jsdom": "^23.2.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2", "vitest": "^3.2.2"}}